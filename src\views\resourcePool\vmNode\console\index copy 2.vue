<template>
  <div class="resource-pool-container">
    <div class="left-information-area">
      <div class="colony-information-area">
        <p class="information-title"  @click="summaryQuery"><span>集群信息</span></p>
        <!-- <p class="information-title"><span>集群信息</span></p> -->
        <div class="colony-content">
          <div><span>UUID:</span><span></span><span>{{ state.uuid }}</span></div>
          <div><span>虚拟机状态:</span><span></span><span>{{ state.status }} 台</span></div>
          <div><span>物理机IP:</span><span></span><span>{{ state.ip }} 台</span></div>
          <div><span>控制台类型:</span><span></span><span>{{ state.type }}</span></div>
          <div><span>VNC/SPICE端口号:</span><span></span><span>{{ state.port }}</span></div>
          <div><span>远程控制台:</span><span></span><span style="cursor:pointer;color:green" @click="consoleClick"> 控制台 </span></div>
        </div>
        <div class="terminal-area">
          <SpiceVnc ref="SVref" />
          <!-- <iframe src="http://************:8123/" frameborder="0" class="terminal-iframe"></iframe> -->
        </div>
      </div>
    </div>
    <div class="right-information-area">
      <div class="strategy-information-area">
        <p class="information-title"><span>策略配置</span></p>
        <div class="information-content">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
          >
            <!-- 操作 -->
				    <template #operation="{ row }">
              <el-button type="primary" plain @click="refresh">配置</el-button>
            </template>
          </my-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { dayjs } from 'element-plus';
import { vmOverview } from '/@/api/ResourcePool'; // 接口
import { capacityConversion } from '/@/model/resource.ts'; // 表格 正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const SpiceVnc = defineAsyncComponent(() => import('/@/views/resourcePool/vmNode/console/SpiceVnc.vue'));

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  columns: [
    { label: 'MAC地址', prop: 'mac', sortable: true, align: 'left' },
    { label: 'IP地址', tdSlot: 'ip' },
	  { label: '操作', tdSlot: 'operation', wrap: true },
  ] as Array<MyTableColumns>, // 表格表头配置
  pagination: {
		show: false,
	}, // 是否显示分页
  tableSelect: [],
  uuid: '-',
  status: '-',
  ip: '-',
  type: '-',
  port: '-',
  cpuRun: '-',
  stor: '-',
  storUsed: '-',
});
// 概要 数据
const summaryQuery=()=>{
	// let libs = ['测试1', '测试2', '测试3', '测试4'];
  // state.uuid = libs[Math.round(Math.random() * 3)]
  vmOverview(props.treeItem.id).then(res=>{
    state.status = res.data.host_count
    state.ip = res.data.vm_count
  })
}
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async(resolve)=>{
    return resolve({
      data: [{mac:'192.168.100',ip:'***************'}], // 数据
      total: 1 // 总数
    })
    // await vmOverview({
    //   host_id: props.treeItem.id,
    //   page: page.pageNum, // 当前页
    //   pagecount: page.pageSize, // 每页显示条数
    //   order_type: page.order, // 排序规则
    //   order_by: page.sort, // 排序列
    //   // page: page.pageNum?page.pageNum:1, // 当前页
    //   // pagecount: page.pageSize?page.pageSize:10, // 每页显示条数
    //   // order_type: page.order?page.order:'asc', // 排序规则
    //   // order_by: page.sort?page.sort:'', // 排序列
    //   search_str: state.tableSearch // 搜索条件
    // }).then((res:any)=>{
    //   resolve({
    //     data: res.data, // 数据
    //     total: res.total*1 // 总数
    //   }) 
    // }).catch((err:any) => {})
  })
};
const SVref = ref();
// SVref.value.openDialog('************:5900')
const consoleClick = () => {
  // console.log('控制台',props.treeItem)
  SVref.value.openDialog('************:5900')
  // window.open('http://************:8123/', '_blank')
}
const refresh = () => {
  console.log('操作')
}

onMounted(() => {
  // console.log('加载')
  // summaryQuery()
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: calc(100%);
	  height: calc(100%);
    display: flex;
    justify-content: space-between;
    .left-information-area {
      width: 600px;
      height: calc(100%);
      padding: 5px;
      .colony-information-area {
        width: calc(100%);
        height: calc(100%);
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .colony-content {
          width: 100%;
          // height: calc(100% - 50px);
          height: 300px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          div {
            span {
              display: inline-block;
              width: 240px;
            }
            span:first-child {
              text-align: right;
            }
            span:nth-child(2) {
              width: 100px;
            }
            span:nth-child(3) {
              width: 240px;
            }
          }
        }
        .terminal-area {
          width: calc(100% - 20px);
          height: calc(100% - 350px);
          background: #414141;
          padding: 5px;
          margin-left: 10px;
          .terminal-iframe {
            width: 100%;
            height: 100%;
            border: none;
          }
        }
      }
    }
    .right-information-area {
      width: calc(100% - 620px);
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .strategy-information-area {
        width: 100%;
        height: calc(100%);
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .information-content {
          padding-top: 30px;
          width: calc(100%);
          height: calc(100% - 100px);
          position: relative;
        }
      }
    }
    
  }
  .information-title {
    height: 40px;
    display: flex;
    align-items: flex-end;
    span {
      display: inline-block;
      text-align: center;
      width: 100px;
      line-height: 30px;
		  background-image: url('/@/assets/resource/title.jpg');
      background-size: 100% 100%;
    }
  }
  .general-content {
    height: 100px;
    .general-img {
      display: flex;
      img {
        width: 80px;
      }
      div {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        p {
          span:nth-child(2) {
            margin-left: 15px;
          }
        }
      }
    }
    .general-progress {
      display: flex;
      justify-content: space-between;
      .el-progress {
        width: 350px;
      }
    }
  }
</style>