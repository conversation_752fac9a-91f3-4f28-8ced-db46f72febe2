// 计算rem转换px的数值
export const transformBySize = (rem: string) => {
	const html = document.querySelector('html');
	if (html) {
		const fontSize = html.style.getPropertyValue('font-size');
		const result = parseFloat(rem.replace('rem', '')) * parseFloat(fontSize.replace('px', '')) + 'px';
		return result;
	}
	return '0px';
};

// 计算rem转换px的数值
export const transformBySizeNumber = (rem: number) => {
	const html = document.querySelector('html');
	if (html) {
		const fontSize = parseFloat(html.style.getPropertyValue('font-size').replace('px', ''));
		const result = rem * fontSize;
		return result;
	}
	return 0;
};

// 获取根字体大小
export const getRootSize = () => {
	return parseFloat(document.getElementsByTagName('html')[0].style.fontSize.split('px')[0]);
};
