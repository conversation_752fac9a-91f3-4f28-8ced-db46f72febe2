<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="auto">
		<div class="prompt-area">
			<p>
				<el-icon color="#fe944d"><WarningFilled /></el-icon>
				选择分配的资源，所有后续操作都依赖于当前选择的资源。请慎重进行选择。
			</p>
		</div>
		<el-form-item label="虚拟机" prop="card">
			<el-input v-model="formItem.card" placeholder="请输入分布式交换机名称" v-show="false" />
			<span>已选 （ {{ formItem.card.length }} ）</span>
		</el-form-item>
		<div class="ztree-publick">
			<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes" @returnOK="returnOK"></ZtreePublick>
		</div>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts';
import { propName } from '/@/model/resource.ts'; // 表列、正则
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口

const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['zeroOK']);
const formItem = reactive({
	name: '',
	card: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});
const propCard = (rule: any, value: any, callback: any) => {
	if (!value || value.length === 0) {
		callback(new Error('请勾选下面虚拟机'));
	} else {
		callback();
	}
}; 
const rulesForm = reactive<FormRules>({
	card: [{ validator: propCard, trigger: 'change' }],
});

const treeData = () => {
	if(!true) {
		formItem.zNodes = [
			{ id: '100', name: '集群1', pid: '' },
			{ id: '1', name: '主机1', pid: '100' },
			{ id: '4', name: '网卡1', pid: '1' },
			{ id: '5', name: '网卡2', pid: '1' },
		];
	}else {
		resourceTreeQuery()
			.then((res) => {
				formItem.zNodes = res.data;
			})
			.catch((error) => {});
	}
};
const returnOK = (val: any,type: any) => {
	if(type == 'none') {
		formItem.card = []
	}else {
		formItem.card = val.filter((node: any) => node.level === 4);
	}
};

watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			// formRef.value.validate((val) => {
				// if (val) {
					emit('zeroOK', formItem);
				// }
			// });
		}
	}
);
setTimeout(() => {
	formItem.type = '分布式存储' + new Date();
	treeData();
}, 500);
</script>
<style lang="scss" scoped>
.prompt-area {
	margin-bottom: 20px;
	p {
		color: #ccc;
		.el-icon {
			margin-right: 5px;
		}
	}
}
.ztree-publick {
	height: 400px;
}
</style>

