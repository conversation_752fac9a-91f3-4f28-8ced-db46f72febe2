/**
 * pinia 类型定义
 */

// 用户信息
declare interface UserInfos<T = any> {
	authBtnList: string[];
	photo: string;
	roles: string[];
	time: number;
	userName: string;
	[key: string]: T;
}
declare interface UserInfosState {
	userInfos: UserInfos;
}

// 路由缓存列表
declare interface KeepAliveNamesState {
	keepAliveNames: string[];
	cachedViews: string[];
}

// 后端返回原始路由(未处理时)
declare interface RequestOldRoutesState {
	requestOldRoutes: string[];
}

// TagsView 路由列表
declare interface TagsViewRoutesState<T = any> {
	tagsViewRoutes: T[];
	isTagsViewCurrenFull: Boolean;
}

// 路由列表
declare interface RoutesListState<T = any> {
	routesList: T[];
	isColumnsMenuHover: Boolean;
	isColumnsNavHover: Boolean;
}

// 布局配置
declare interface ThemeConfigState {
	themeConfig: {
		isDrawer: boolean;
		primary: string;
		topBar: string;
		topBarColor: string;
		isTopBarColorGradual: boolean;
		menuBar: string;
		menuBarColor: string;
		menuBarActiveColor: string;
		isMenuBarColorGradual: boolean;
		columnsMenuBar: string;
		columnsMenuBarColor: string;
		isColumnsMenuBarColorGradual: boolean;
		isColumnsMenuHoverPreload: boolean;
		isCollapse: boolean;
		isUniqueOpened: boolean;
		isFixedHeader: boolean;
		isFixedHeaderChange: boolean;
		isClassicSplitMenu: boolean;
		isLockScreen: boolean;
		lockScreenTime: number;
		isShowLogo: boolean;
		isShowLogoChange: boolean;
		isBreadcrumb: boolean;
		isTagsview: boolean;
		isBreadcrumbIcon: boolean;
		isTagsviewIcon: boolean;
		isCacheTagsView: boolean;
		isSortableTagsView: boolean;
		isShareTagsView: boolean;
		isFooter: boolean;
		isGrayscale: boolean;
		isInvert: boolean;
		isIsDark: boolean;
		isWartermark: boolean;
		wartermarkText: string;
		tagsStyle: string;
		animation: string;
		columnsAsideStyle: string;
		columnsAsideLayout: string;
		layout: string;
		isRequestRoutes: boolean;
		globalTitle: string;
		globalViceTitle: string;
		globalViceTitleMsg: string;
		globalI18n: string;
		globalComponentSize: string;
		info: EmptyObjectType;
		[key: string]: T;
	};
}
// 路由缓存列表
declare interface permisInfo {
	userName: string;
	role: string;
	ceshi: Boolean;





	tianjiazhujichi: Boolean; // 添加主机池
	xiugaizhujichi: Boolean; // 修改主机池
	shanchuzhujichi: Boolean; // 删除主机池
	tianjiajiqun: Boolean; // 添加集群
	xiugaijiqun: Boolean; // 修改集群
	shanchujiqun: Boolean; // 删除集群
	jiqunHA: Boolean; // 集群HA
	jiqunDRS: Boolean; // 集群DRS
	tianjiawuliji: Boolean; // 添加物理机
	xiugaiwuliji: Boolean; // 修改物理机
	shanchuwuliji: Boolean; // 删除物理机
	guanbiwuliji: Boolean; // 关闭物理机
	chongqiwuliji: Boolean; // 重启物理机
	tuichuwuliji: Boolean; // 退出物理机
	weihumoshi: Boolean; // 维护模式
	tianjiaxuniji: Boolean; // 添加虚拟机
	xunijikaiji: Boolean; // 虚拟机开机
	xunijiguanji: Boolean; // 虚拟机关机
	xunijichongqi: Boolean; // 虚拟机重启
	xunijishanchu: Boolean; // 虚拟机删除
	xunijiqiangzhichongqi: Boolean; // 虚拟机强制重启
	xunijiguanbidianyuan: Boolean; // 虚拟机关闭电源
	xunijizanting: Boolean; // 虚拟机暂停
	xunijihuifu: Boolean; // 虚拟机恢复
	xunijiwanquankelong: Boolean; // 虚拟机完全克隆
	xunijilianjiekelong: Boolean; // 虚拟机链接克隆
	xunijiqianyi: Boolean; // 虚拟机迁移
	xunijikongzhitai: Boolean; // 虚拟机控制台
	xunijixiugai: Boolean, // 虚拟机修改
	ziyuanjiediangaiyao: Boolean; // 资源节点概要
	zhujichigaiyao: Boolean; // 主机池概要
	jiqungaiyao: Boolean; // 集群概要
	zhujigaiyao: Boolean; // 主机概要
	xunijigaiyao: Boolean; // 虚拟机概要
	
	zhujichi: Boolean;
	jiqun: Boolean;
	wuliji: Boolean;
	xuniji: Boolean;
	cunchuchi: Boolean;
	fenbushijiaohuanji: Boolean;
	bendijiaohuanji: Boolean;
	yingjianshebei: Boolean;
	moban: Boolean;
	huishouzhan: Boolean;
	xunijibeifen: Boolean;
	xunijikuaizhao: Boolean;
	yunxingrizhi: Boolean;
	qianyirizhi: Boolean;
	xingnengjiankong: Boolean;
	gaojing: Boolean;
	renwu: Boolean;
}
