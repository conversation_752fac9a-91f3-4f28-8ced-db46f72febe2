/**
 * pinia 类型定义
 */

// 权限信息
declare interface permisInfo {
	userName: string;
	role: string;
	ceshi: boolean;
	// 资源池
	tianjiazhujichi: boolean; // 添加主机池
	xiugaizhujichi: boolean; // 修改主机池
	shanchuzhujichi: boolean; // 删除主机池
	tianjiajiqun: boolean; // 添加集群
	xiugaijiqun: boolean; // 修改集群
	shanchujiqun: boolean; // 删除集群
	jiqunHA: boolean; // 集群HA
	jiqunDRS: boolean; // 集群DRS
	tianjiawuliji: boolean; // 添加物理机
	xiugaiwuliji: boolean; // 修改物理机
	shanchuwuliji: boolean; // 删除物理机
	guanbiwuliji: boolean; // 关闭物理机
	chongqiwuliji: boolean; // 重启物理机
	tuichuwuliji: boolean; // 退出物理机
	weihumoshi: boolean; // 维护模式
	tianjiaxuniji: boolean; // 添加虚拟机
	xunijikaiji: boolean; // 虚拟机开机
	xunijiguanji: boolean; // 虚拟机关机
	xunijichongqi: boolean; // 虚拟机重启
	xunijishanchu: boolean; // 虚拟机删除
	xunijiqiangzhichongqi: boolean; // 虚拟机强制重启
	xunijiguanbidianyuan: boolean; // 虚拟机关闭电源
	xunijizanting: boolean; // 虚拟机暂停
	xunijihuifu: boolean; // 虚拟机恢复
	xunijiwanquankelong: boolean; // 虚拟机完全克隆
	xunijilianjiekelong: boolean; // 虚拟机链接克隆
	xunijiqianyi: boolean; // 虚拟机迁移
	xunijikongzhitai: boolean; // 虚拟机控制台
	xunijixiugai: boolean; // 虚拟机修改
	ziyuanjiediangaiyao: boolean; // 资源节点概要
	zhujichigaiyao: boolean; // 主机池概要
	jiqungaiyao: boolean; // 集群概要
	zhujigaiyao: boolean; // 主机概要
	xunijigaiyao: boolean; // 虚拟机概要
	zhujichi: boolean;
	jiqun: boolean;
	wuliji: boolean;
	xuniji: boolean;
	cunchuchi: boolean;
	fenbushijiaohuanji: boolean;
	bendijiaohuanji: boolean;
	yingjianshebei: boolean;
	moban: boolean;
	huishouzhan: boolean;
	xunijibeifen: boolean;
	xunijikuaizhao: boolean;
	yunxingrizhi: boolean;
	qianyirizhi: boolean;
	xingnengjiankong: boolean;
	gaojing: boolean;
	renwu: boolean;
}

// 用户信息
declare interface UserInfos<T = any> {
	authBtnList: string[];
	photo: string;
	roles: string[];
	time: number;
	userName: string;
	[key: string]: T;
}
declare interface UserInfosState {
	userInfos: UserInfos;
}

// 路由缓存列表
declare interface KeepAliveNamesState {
	keepAliveNames: string[];
	cachedViews: string[];
}

// 后端返回原始路由(未处理时)
declare interface RequestOldRoutesState {
	requestOldRoutes: string[];
}

// TagsView 路由列表
declare interface TagsViewRoutesState<T = any> {
	tagsViewRoutes: T[];
	isTagsViewCurrenFull: Boolean;
}

// 路由列表
declare interface RoutesListState<T = any> {
	routesList: T[];
	isColumnsMenuHover: Boolean;
	isColumnsNavHover: Boolean;
}

// 布局配置
declare interface ThemeConfigState {
	themeConfig: {
		isDrawer: boolean;
		primary: string;
		topBar: string;
		topBarColor: string;
		isTopBarColorGradual: boolean;
		menuBar: string;
		menuBarColor: string;
		menuBarActiveColor: string;
		isMenuBarColorGradual: boolean;
		columnsMenuBar: string;
		columnsMenuBarColor: string;
		isColumnsMenuBarColorGradual: boolean;
		isColumnsMenuHoverPreload: boolean;
		isCollapse: boolean;
		isUniqueOpened: boolean;
		isFixedHeader: boolean;
		isFixedHeaderChange: boolean;
		isClassicSplitMenu: boolean;
		isLockScreen: boolean;
		lockScreenTime: number;
		isShowLogo: boolean;
		titleColor: string;
		isShowLogoChange: boolean;
		isBreadcrumb: boolean;
		isTagsview: boolean;
		isBreadcrumbIcon: boolean;
		isTagsviewIcon: boolean;
		isCacheTagsView: boolean;
		isSortableTagsView: boolean;
		isShareTagsView: boolean;
		isFooter: boolean;
		isGrayscale: boolean;
		isInvert: boolean;
		isIsDark: boolean;
		isWartermark: boolean;
		wartermarkText: string;
		tagsStyle: string;
		animation: string;
		columnsAsideStyle: string;
		columnsAsideLayout: string;
		layout: string;
		isRequestRoutes: boolean;
		globalTitle: string;
		globalViceTitle: string;
		globalViceTitleMsg: string;
		globalI18n: string;
		info: EmptyObjectType;
	};
}
