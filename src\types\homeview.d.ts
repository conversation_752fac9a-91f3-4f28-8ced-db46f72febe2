// 概览--基本信息
declare type SoftwareInfo<T = any> = {
	software: string; // 软件名称
	version: string; // 版本
	serial_number: string; // 设备序列号
	cpu_info: Array<CpuInfo>; // cpu数组
	service_status: string /*服务运行状态*/;
	run_time: string /*系统运行时长*/;
	fan_status: string;
	[key: string]: T;
};

// 资源使用率数据
declare type resourceInfo<T = any> = {
	mem_info: {
		mem_total: string; // 总量 kb
		mem_free: string; // 空闲 kb
		mem_available: string; // 使用 kb
	}; // 内存信息
	cpu_usage_rate: 0; // cpu使用率%
	system_status: true; // 系统状态
	[key: string]: T;
};

declare type CpuInfo<T = any> = {
	cpu_module: string; // CPU型号
	cpu_cores: number; // CPU核心数量
};

declare type CpuInfomation<T = any> = {
	Name: string; // cpu名称
	Value: string; // cpu温度
	Unit: string; // 温度单位
	State: string; // 状态
	[key: string]: T;
};

// 硬件信息--电源信息
declare type PowerInfo<T = any> = {
	status: string /*电源状态*/;
	revision: string; // 电源类型
	serial_number: string /*SN，设备序列号*/;
	manufacturer: string /*制造商*/;
	[key: string]: T;
};

// 硬件信息--风扇信息
declare type FanInfo<T = any> = {
	id: string | number; // id
	name: string; // 风扇名称
	status: string; // 风扇状态
	fan_speed: string; // 风扇速度
	fanSpeed: number; // 风扇速度
};

// 硬件信息--卷池信息
declare type PoolInfo<T = any> = {
	Name: string; //卷池名称
	Health: string; //卷池状态
	Size: number; //总容量
	Free: number; //可用容量
	[key: string]: T;
};

// 磁盘信息
declare type DiskInfo<T = any> = {
	name: string; // 磁盘名称
	serial: string; // 磁盘序列号
	model: string; // 磁盘型号
	size: string; // 磁盘大小
	state: string; // 磁盘状态
};
