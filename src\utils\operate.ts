import { ElMessage, ElMessageBox } from 'element-plus';

declare type deleteConfig = {
	data: EmptyObjectType | number | string;
	textForApi?: boolean; // 反馈是否来自接口
	successApi?: string; // 成功字段
	errorApi?: string; // 失败字段
	confirmText?: string; // 询问内容
	successText?: string; // 成功反馈
	cancelText?: string; // 取消反馈
	errorText?: string; // 错误反馈
	other?: Function; // 删除成功后的回调函数 只需要传递函数,不需要执行
};

/**
 * 删除对话框
 * @param { deleteConfig } config 配置
 * @param api 接口 只需要传递函数,不需要执行
 */
export const deleteMessage = (config: deleteConfig, api: Function) => {
	const c = {
		confirmText: '是否要删除该数据？',
		successText: '删除成功！',
		cancelText: '取消删除!',
		errorText: '删除失败，请联系管理员！',
		textForApi: false,
		successApi: 'data',
		errorApi: 'message',
		...config,
	} as deleteConfig;

	ElMessageBox.confirm(c.confirmText, '提示', {
		distinguishCancelAndClose: true,
		confirmButtonText: '删除',
		cancelButtonText: '取消',
	})
		.then(() => {
			api(config.data).then((res: EmptyObjectType) => {
				if (res.code === 2000) {
					if (c.textForApi && c.successApi) {
						c.successText = res[c.successApi];
					}
					ElMessage({
						type: 'success',
						message: c.successText,
					});
					if (c.other) {
						c.other();
					}
				} else {
					if (c.textForApi && c.errorApi) {
						c.errorText = res[c.errorApi];
					}
					ElMessage({
						type: 'info',
						message: c.errorText,
					});
				}
			});
		})
		.catch(() => {
			ElMessage({
				type: 'info',
				message: c.cancelText,
			});
		});
};
