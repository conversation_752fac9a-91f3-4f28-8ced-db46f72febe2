<template>
  <el-dialog
    v-model="state.isShow"
    title="修改分布式交换机"
    append-to-body
    class="dialog-500"
  >
    <el-form
      ref="ruleFormRef"
      :model="state"
      :rules="rules"
      label-width="auto"
    >
      <el-form-item label="虚拟交换机名称" prop="name">
        <el-input v-model="state.name"  placeholder="请输入虚拟交换机名称"/>
      </el-form-item>
      <el-form-item label="MTU(字节)" prop="mtu">
        <el-input v-model="state.mtu" type="number" :min=1 />
      </el-form-item>
      <el-form-item label="BIND" prop="bind">
        <el-select v-model="state.bind"  style="width: 100%">
          <el-option label="BIND 0" value="0"></el-option>
          <el-option label="BIND 1" value="1"></el-option>
          <el-option label="BIND 2" value="2"></el-option>
          <el-option label="BIND 3" value="3"></el-option>
          <el-option label="BIND 4" value="4"></el-option>
          <el-option label="BIND 5" value="5"></el-option>
          <el-option label="BIND 6" value="6"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="table-area">
      <my-table
        ref="tableRef"
        :pagination="state.pagination"
        :columns="state.columns"
        :request="getTableData"
        @selectionChange='selectChange'
      >
      </my-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { localSwitchEdit,adapterQuery } from '/@/api/Network/index.ts'; // 接口
import { propName,adapterColumns } from '/@/model/network.ts'; // 表列、正则
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const ruleFormRef = ref<FormInstance>()
// 定义变量内容
const state = reactive({
  isShow: false,
  columns: adapterColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  name: '',
  mtu: '1500',
  bind: '0',
  dir: '',
  id: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "change" },
  ],
  mtu: [{ required: true, message: '必选项', trigger: 'blur' }],
})
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
	return new Promise(async(resolve)=>{
    adapterQuery({
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
  }
}
const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        state.isShow = false;
        localSwitchEdit({
          name: state.name,
          id: state.id,
          mtu: state.mtu,
          // bind: state.bind,
          // adapter: state.tableSelect,
        })
        .then((res:any) => {
          emit('returnOK', 'refresh');
        })
      }
    })
  }
}
// 打开弹窗
const openDialog = async (row: any,treeItem: any) => {
	nextTick(() => {
		state.isShow = true;
    state.name = row.name
    state.mtu = row.mtu
    state.id = row.id
    if(tableRef.value){
			refresh();
		}
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.table-area {
	height: 200px;
	width: 100%;
	position: relative;
}
</style>