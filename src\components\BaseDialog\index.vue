<template>
  <el-dialog v-model="dialogVisible" :title="title" :width="width" :show-close="false" :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape">
    <template #header>
      <div class="custom-header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="close-btn" @click="handleCancel">
          <el-icon>
            <CircleCloseFilled />
          </el-icon>
        </div>
      </div>
    </template>
    
    <slot></slot>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="default" @click="handleCancel">{{ cancelText }}</el-button>
        <el-button :type="confirmType" @click="handleConfirm">{{ confirmText }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { CircleCloseFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  width: {
    type: String,
    default: '35%'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmType: {
    type: String,
    default: 'warning'
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  closeOnPressEscape: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const dialogVisible = ref(props.modelValue)

watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val)
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script> 