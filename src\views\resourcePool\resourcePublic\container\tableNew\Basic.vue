<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="名称" prop="name">
			<el-input v-model="formItem.name" placeholder="请输入名称" />
		</el-form-item>
    <el-form-item label="镜像名称" prop="imgName">
			<el-input v-model="formItem.imgName" placeholder="请输入镜像名称" />
		</el-form-item>
    <el-form-item label="镜像驱动">
      <el-select v-model="formItem.driver" style="width: 100%">
        <el-option label="Docker Hub" value="docker" />
        <el-option label="Glance" value="glance" disabled />
      </el-select>
		</el-form-item>
		<el-form-item label="镜像拉取策略">
      <el-select v-model="formItem.policy" style="width: 100%">
        <el-option label="如果没有，才进行下载" value="ifnotpresent" />
        <el-option label="总是下载" value="always" />
        <el-option label="从不下载" value="never" />
      </el-select>
		</el-form-item>
    <!-- <el-form-item label="选择网络" prop="netID"> -->
    <el-form-item label="选择网络">
      <el-select v-model="formItem.netID" style="width: 100%">
        <el-option v-for="item in formItem.netData" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
		</el-form-item>
    <el-form-item label="命令">
			<el-input v-model="formItem.command" placeholder="请输入要发送到容器的命令" />
		</el-form-item>
    <el-form-item label="创建后启动容器">
			<el-checkbox v-model="formItem.start" />
		</el-form-item>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { propName } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  besicTime: {
    type: String,
    required: true
  }
});
const formRef = ref<FormInstance>()
const emit = defineEmits(['basicOK']);
// 基本信息
const formItem = reactive({
  name: '',
  imgName: '',
  driver: 'docker',
  policy: '',
  netID: '',
  netName: '',
  command: '',
  start: true,
  netData: [],
});
const rulesForm = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "blur" },
  ],
  imgName: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "blur" },
  ],
  netID: [{ required: true, message: '必选项', trigger: 'blur' }],
})
watch(
  ()=> props.besicTime,
  (val)=>{
    if (formRef.value) {
      formRef.value.validate(val=>{
        if (val) {
          emit('basicOK', {
            name: formItem.name,
            image: formItem.imgName,
            image_driver: formItem.driver,
            image_pull_policy: formItem.policy,
            nets: {networks:[{uuid:formItem.netID,name:formItem.netName}]},
            command: formItem.command,
            run: formItem.start,
          });
        }
      })
    }
  }
);
</script>