@use './custom.scss' as *;
:root {
    --el-bg-color-white: #ffffff;
    --el-color-primary: #3267FF;
    --el-color-primary-light-3: rgba(36, 85, 227, 1);
    --el-color-primary-light-5: rgba(70, 117, 255, 1);
    --el-color-primary-light-7: rgba(92, 134, 255, 1);
    --el-color-primary-light-8: rgba(134, 165, 255, 1);
    --el-color-primary-light-9: rgba(178, 198, 255, 1);
    --el-color-primary-dark-2: rgba(203, 216, 255, 1);
    --el-color-primary-light-10: rgba(126,166,255,1);
    --el-color-primary-light-11: rgba(199,215,253,1);

    --el-color-success: #67C23A;
    --el-color-success-light-3: rgba(82, 155, 46, 1);
    --el-color-success-light-5: rgba(149, 212, 117, 1);
    --el-color-success-light-7: rgba(179, 225, 157, 1);
    --el-color-success-light-8: rgba(209, 237, 196, 1);
    --el-color-success-light-9: rgba(225, 243, 216, 1);
    --el-color-success-dark-2: rgba(240, 249, 235, 1);
    --el-color-warning: #FE6902;
    --el-color-warning-light-3: rgba(235, 101, 5, 1);
    --el-color-warning-light-5: rgba(255, 125, 35, 1);
    --el-color-warning-light-7: rgba(255, 148, 73, 1);
    --el-color-warning-light-8: rgba(255, 181, 129, 1);
    --el-color-warning-light-9: rgba(255, 207, 174, 1);
    --el-color-warning-dark-2: rgba(255, 226, 205, 1);
    --el-color-danger: #f56c6c;
    --el-color-danger-light-3: rgba(196, 86, 86, 1);
    --el-color-danger-light-5: rgba(248, 152, 152, 1);
    --el-color-danger-light-7: rgba(250, 182, 182, 1);
    --el-color-danger-light-8: rgba(252, 211, 211, 1);
    --el-color-danger-light-9: rgba(253, 226, 226, 1);
    --el-color-danger-dark-2: rgba(254, 240, 240, 1);
    --el-color-info: #909399;
    --el-color-info-light-3: rgba(115,118,122,1);
    --el-color-info-light-5: rgba(177,179,184,1);
    --el-color-info-light-7: rgba(200,201,204,1);
    --el-color-info-light-8: rgba(222,223,224,1);
    --el-color-info-light-9: rgba(234,234,234,1);
    --el-color-info-dark-2: rgba(244,244,245,1);
    --el-color-default: #EBECF0;
    --el-color-default-light-3: rgba(255,255,255,1);
    --el-color-default-light-5: rgba(235, 236, 240, 1);
    --el-color-default-light-7: rgba(255,255,255,1);
    --el-color-default-light-8: rgba(235, 236, 240, 1);
    --el-color-default-light-9: rgba(235,236,240,1);
    --el-color-default-dark-2: rgba(235, 236, 240, 1);
    --el-color-default-dark-3: #99999B;

    --el-bg-color: #ffffff;
    --el-bg-color-page: #f2f3f5;
    --el-bg-color-overlay: #ffffff;
    --el-text-color-primary: #303133;
    --el-text-color-regular: #606266;
    --el-text-color-secondary: #909399;
    --el-text-color-placeholder: #a8abb2;
    --el-text-color-disabled: #c0c4cc;
    --el-border-color: #dcdfe6;
    --el-border-color-light: #e4e7ed;
    --el-border-color-lighter: #ebeef5;
    --el-border-color-extra-light: #f2f6fc;
    --el-border-color-dark: #d4d7de;
    --el-border-color-darker: #cdd0d6;
    --el-fill-color: #f0f2f5;
    --el-fill-color-light: #f5f7fa;
    --el-fill-color-lighter: #fafafa;
    --el-fill-color-extra-light: #fafcff;
    --el-fill-color-dark: #ebedf0;
    --el-fill-color-darker: #e6e8eb;
    --el-fill-color-blank: #ffffff;
    --el-color-black: #333333;
    --el-color-gray: #B9B9B9;
    --el-color-gray-light: rgba(98,99,102,1);
}

// 按钮
.el-button{
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 12px;
  padding-bottom: 12px;
  height: 40px;
  min-width: 80px;
  border-radius: 6px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  -o-border-radius: 6px;
}
.el-button--warning{
  &:hover{
    background-color: var(--el-color-warning-light-7);
    border-color: var(--el-color-warning-light-7);
  }
  &:active{
    background-color: var(--el-color-warning-light-3);
    border-color: var(--el-color-warning-light-3);
  }
  &:disabled{
    background-color: var(--el-color-warning-light-9);
    border-color: var(--el-color-warning-light-9);
  }
}
.el-button--primary{
  &:hover{
    background-color: var(--el-color-primary-light-7);
    border-color: var(--el-color-primary-light-7);
  }
  &:active{
    background-color: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }
  &:disabled{
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary-light-9);
  }
}
.el-button--default{
  color: var(--el-color-black);
  background-color: var(--el-color-default);
  &:hover{
    background-color: var(--el-color-default-light-7);
    border-color: var(--el-color-default-dark-3);
    color: var(--el-color-black);
  }
  &:active{
    background-color: var(--el-color-default-light-3);
    border-color: var(--el-color-default-dark-3);
    color: var(--el-color-black);
  }
  &:disabled{
    background-color: var(--el-color-default-light-9);
    border-color: var(--el-color-default-light-9);
    color: var(--el-color-gray);
  }
}

.el-button--danger{
  &:hover{
    background-color: var(--el-color-danger-light-7);
    border-color: var(--el-color-danger-light-7);
  }
  &:active{
    background-color: var(--el-color-danger-light-3);
    border-color: var(--el-color-danger-light-3);
  }
  &:disabled{
    background-color: var(--el-color-danger-light-9);
    border-color: var(--el-color-danger-light-9);
  }
}

.el-button--secondary{
 
  &:hover{
    background-color: #FEF0E5;
    border-color: #FFCFAE;
    color: #FE6902;
  }
  &:active{
    background-color: #FEF0E5;
    border-color: #FE6902;
    color: #FE6902;
  }
  &:disabled{
    background-color: #FFFFFF;
    border-color: #DCDFE6;
    color: #D8DDE8;
  }
}

// 文本
.el-text.el-text--primary{
  &:hover{
    cursor: pointer;
    color: var(--el-color-primary-light-10);
  }
  &:active{
    color: var(--el-color-primary-light-3);
  }
  &:disabled{
    color: var(--el-color-primary-light-11);
  }
  &.link-text{
    &:hover{
      text-decoration: underline;
    }
    &:active{
      text-decoration: underline;
    }
  }
}

// 修改滚动条轨道的宽度、高度
.el-scrollbar__track{
  width: 6px;
  height: 6px;
}
// 修改滚动条的滑块的宽度、高度、颜色、radius,所有颜色都是--el-color-warning
.el-scrollbar__thumb{
  width: 6px;
  height: 6px;
  background-color: var(--el-color-warning);
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  opacity:1;
  
}

// 修改菜单的样式
.el-menu.el-menu--vertical{
  width: 220px;
  height: 100%;
  background-color: var(--el-bg-color);
  box-shadow: 0px 2px 26px 0px rgba(58,25,2,0.09);
  padding: 29px 5px;
  .el-menu-item{
    padding-left: 33px;
    height: 50px;
    line-height: 50px;
    border-radius: 4px;
    color: var(--el-color-gray-light);



    // &:not(&:last-child){
    //   margin-bottom: 20px;
    // }
    &:hover{
      background-color: transparent;
      color: #FF6902;
    }
    &.is-active{
      background-color: #FFE2CD;
      font-weight: 600;
      color: #FF6902;
    }
  }
  .el-sub-menu__title{
    &:hover{
      background-color: transparent;
      color: #FF6902;
    }
  }
  .el-sub-menu{
    .el-menu-item{
      &:hover{
        background-color: transparent;
        color: #FF6902;
      }
      &.is-active{
        background-color: #FFE2CD;
        font-weight: 600;
        color: #FF6902;
      }
    }
    &.is-active .el-sub-menu__title{
      background-color: transparent;
      font-weight: 600;
      color: #FF6902;
    }
  }
}

// 修改下拉菜单的样式
.el-dropdown__popper{
  width: 167px;
  border-radius: 8px;
 
 
  
  &.is-pure{
    padding-left: 4px;
    padding-right: 4px;
  }
  &.el-popper{
    background-color: var(--el-bg-color);
    box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.12);
    border: 1px solid var(--el-border-color-light);
  }
  .el-dropdown-menu__item{
    color: var(--el-color-black);
    font-size: 14px;
    font-family: PingFangSC-regular;
    border-radius: 4px;
    &:hover{
      color: var(--el-color-warning);
      background-color: transparent;
    }
    &.is-disabled{
      color: var(--el-text-color-disabled);
    }
  }
  .is-selected {
    background-color: var(--el-color-warning-dark-2);
    color: var(--el-color-warning);
    &:hover{
      background-color: var(--el-color-warning-dark-2);
      color: var(--el-color-warning);
    }
  }

}
.el-dropdown{
  color: var(--el-color-black);
  .el-icon{
    // color: var(--el-color-warning);
    margin-left: 14px;
    svg{
      width: 12px;
      height: 12px;
    }
  }
  .dropdown-trigger{
    display: flex;
    align-items: center;
    min-width: 100px;
    cursor: pointer;
  }
}

// 面包屑
.el-breadcrumb__inner{
  color: #31313B;
  font-size: 14px;
  font-weight: 400 !important;
}
.el-breadcrumb__inner a, .el-breadcrumb__inner.is-link,
.el-breadcrumb__item:last-child .el-breadcrumb__inner, .el-breadcrumb__item:last-child .el-breadcrumb__inner a, .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover, .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover{
  // color: var(--el-color-black);
  color: #999999;
  &:hover{
    color: var(--el-color-warning);
    text-decoration: none;
  }
}

// 标签页
.el-tabs__item.is-active, .el-tabs__item:hover{
  color: #333333;
}
.el-tabs__active-bar{
  background-color: #FF6902;
}
.el-tabs__nav-wrap:after{
  background-color: #F7F4F2;
}
.th-tabs{
  .el-tabs__header{
    height: 50px;
    border-radius: 28px;
    background-color: var(--el-bg-color-white);
    box-shadow: 0px 7px 12px 0px rgba(133,55,3,0.12);
    margin: 0;
    .el-tabs__nav-wrap:after{
      display: none;
    }
    .el-tabs__nav-scroll{
      height: 100%;
      padding-left: 58px;
      padding-right: 58px;
      .el-tabs__active-bar{
        display: none;
      }
    }
    .el-tabs__nav-wrap{
      height: 100%;
      margin-bottom: 0;
    }
    .el-tabs__nav{
      height: 100%;
    }
    .el-tabs__item{
      height: 100%;
      color: #6B6C70;
      font-size: 14px;
      font-weight: 400;
      padding:0 25px !important;
      &.is-active{
        background-color: #FFF2EA;
        color: #FF6902;
        &::before{
          content: '';
          display: block;
          width: 4px;
          height: 28px;
          background-color: var(--el-color-warning);
          position: absolute;
          left: 0;
        }
      }
    }
  }

}

// 输入框
.el-input{
  --el-input-text-color: var(--el-color-black);
  --el-input-focus-border-color: var(--el-color-warning);
  --el-input-hover-border-color: var(--el-text-color-placeholder);
  --el-input-height: 40px;
  --el-input-placeholder-color: var(--el-border-color);
  border-radius: 6px 6px 6px 6px;
  &.is-disabled .el-input__wrapper{
    background-color: var(--el-fill-color-light);
    box-shadow: 0 0 0 0.01rem var(--el-border-color-lighter) inset;
  }
}
.el-input-number__decrease, .el-input-number__increase{
  color: var(--el-color-warning);
}
.el-input-number.is-controls-right .el-input-number__decrease, .el-input-number.is-controls-right .el-input-number__increase{
  --el-input-number-controls-height:20px
}

// 选择器
.el-select{
  --el-select-disabled-color: var(--el-text-color-disabled);
  --el-select-disabled-border: var(--el-disabled-border-color);
  --el-select-close-hover-color: var(--el-color-warning);
  border-radius: 6px 6px 6px 6px;
  .el-select__wrapper{
    min-height: 40px;
    &.is-focused{
      box-shadow: 0 0 0 0.01rem var(--el-color-warning) inset;
    }
    &.is-hovering:not(.is-focused){
      box-shadow: 0 0 0 0.01rem var(--el-text-color-placeholder) inset;
    }
  }
}
.el-select-dropdown{
  border-radius: 8px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -ms-border-radius: 8px;
  -o-border-radius: 8px;
  .el-select-dropdown__list{
    padding-left: 4px;
    padding-right: 4px;
    background-color: var(--el-bg-color-overlay);
    box-shadow: var(--el-box-shadow-light);
    border: 1px solid var(--el-border-color-light);
  }
  .el-select-dropdown__item{
    color: var(--el-color-black);
    font-size: 14px;
    font-family: PingFangSC-regular;
    border-radius: 4px;
    &:hover{
      color: var(--el-color-warning);
      background-color: transparent;
    }
    &.is-disabled{
      color: var(--el-text-color-disabled);
    }
  }
  .is-selected {
    background-color: var(--el-color-warning-dark-2);
    color: var(--el-color-warning);
    &:hover{
      background-color: var(--el-color-warning-dark-2);
      color: var(--el-color-warning);
    }
  }
}

// 复选框
.el-checkbox{
  --el-checkbox-checked-text-color: var(--el-color-warning);
  --el-checkbox-checked-input-border-color: var(--el-color-warning);
  --el-checkbox-checked-bg-color: var(--el-color-warning);
  --el-checkbox-input-border-color-hover: var(--el-color-warning);
}

// 单选框
.el-radio{
  --el-radio-input-border-color-hover: var(--el-color-warning);
  .el-radio__input.is-checked .el-radio__inner{
    background-color: var(--el-color-warning);
    border-color: var(--el-color-warning);
  }
  .el-radio__input.is-checked + .el-radio__label{
    color: var(--el-color-warning);
  }
  .el-radio__input.is-disabled.is-checked .el-radio__inner{
    background-color: var(--el-disabled-bg-color);
    border-color: var(--el-disabled-border-color);
  }
  .el-radio__input.is-disabled+span.el-radio__label{
    color: var(--el-text-color-placeholder);
  }
}

// 进度条
.el-progress-bar__inner{
  background-color: var(--el-color-warning);
}

// 日期选择器
.el-date-picker{
  --el-datepicker-active-color: var(--el-color-warning);
    --el-datepicker-hover-text-color: var(--el-color-warning);
    .el-date-table td.today .el-date-table-cell__text{
      color: var(--el-color-warning);
    }
    .el-date-table td .el-date-table-cell .el-date-table-cell__text:hover{
      border: 1px solid var(--el-color-warning);
    }
    .el-date-table td.current:not(.disabled) .el-date-table-cell__text{
      color: #ffffff;
    }
    .el-input{
      height: 30px;
      line-height: 30px;
    }
    .el-button.is-plain{
      --el-button-hover-text-color: var(--el-color-warning);
      --el-button-hover-border-color: var(--el-color-warning);
      border: transparent;
    }
}
.el-date-editor{
  --el-input-focus-border: var(--el-color-warning);
  --el-input-focus-border-color: var(--el-color-warning);
}
.el-date-range-picker{
  --el-datepicker-active-color: var(--el-color-warning);
  --el-datepicker-hover-text-color: var(--el-color-warning);
  --el-datepicker-inrange-bg-color: #FFF8F2;
  .el-date-table td.today .el-date-table-cell__text{
    color: var(--el-color-warning);
  }
  .el-date-table td .el-date-table-cell .el-date-table-cell__text:hover{
    border: 1px solid var(--el-color-warning);
  }
  .el-date-table td.today.start-date:not(.disabled) .el-date-table-cell__text{
    color: #ffffff;
  }
  .el-date-table td.today.end-date:not(.disabled) .el-date-table-cell__text{
    color: #ffffff;
  }
  .el-date-table{
    border:  1px solid var(--el-datepicker-border-color);
    th{
      border-bottom: transparent;
    }
  }
  .el-date-range-picker__content.is-left{
    border-right: transparent;
  }
  .el-date-range-picker__content{
    padding: 30px;
    padding-top: 20px;
    &.is-left{
      padding-right: 0;
    }
  }
  .el-date-range-picker__header{
    margin-bottom: 10px;
  }
  .el-input{
    height: 30px;
    line-height: 30px;
  }
  .el-button.is-plain{
    --el-button-hover-text-color: var(--el-color-warning);
    --el-button-hover-border-color: var(--el-color-warning);
    border: transparent;
  }
}
.el-range-editor.el-input__wrapper{
  height: 40px;
}
.el-time-panel__btn.confirm{
  color: var(--el-color-warning);
}


// 上传
.el-upload{
  height: 206px;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-upload-dragger {
    border-radius: 6px;
    background-color: rgba(250,252,255,1);
    border: 1px dashed rgba(220,221,223,1);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: border-color 0.3s, background 0.3s;

    &:hover, &.is-dragover {
      border-color: #409eff;
      background: #f0faff;
    }
  }
  
  .el-upload__text em{
    color: var(--el-color-warning) !important;
  }
  &:focus, &:focus .el-upload-dragger{
    border-color: var(--el-color-warning);
  }
  .el-upload-dragger:hover, .el-upload-dragger.is-dragover{
    border-color: var(--el-color-warning) !important;
  }



  .upload-plus {
    font-size: 32px;
    color: #bfbfbf;
    margin-bottom: 8px;
  }

  .main-tip {
    font-size: 15px;
    color: #606266;
    margin-bottom: 4px;

    .upload-link {
      color: #fa541c;
      cursor: pointer;
      margin-left: 2px;
    }
  }

  .sub-tip {
    font-size: 12px;
    color: #bfbfbf;
  }
}
.el-upload-list__item .el-progress{
  margin-top: 20px !important;
  top: 2px;
}

// 滑块
.el-slider{
  --el-slider-height: 4px;
  --el-slider-button-size: 22px;
  --el-slider-button-wrapper-size: 40px;
  --el-slider-button-wrapper-offset: -13px;
  .el-slider__runway{
    background-color: rgba(219,234,254,1);
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}
.el-slider__button-wrapper{
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(51,103,255,0.2);
    top: 32%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 0;
  }
  .el-slider__button{
    background-color: rgba(51,103,255,1);
  }
}
}

// 穿梭框
.el-transfer{
  display: flex;
  align-items: center;
  .el-transfer-panel{
    border-radius: 5px;
    background: rgba(255,255,255,1);
    color: rgba(16,16,16,1);
    
    .el-transfer-panel__header{
      border-radius: 5px 5px 0px 0px;
      background: rgba(109,119,143,1);
      .el-checkbox__label{
        font-size: 14px !important;
        color: rgba(255,255,255,1) !important;
      }
      .el-checkbox .el-checkbox__label span{
        color: rgba(255,255,255,1) !important;
      }
    }
    .el-transfer-panel__item{
      color: #333333;
      &:hover{
        color: var(--el-color-warning);
      }
    }
    .el-transfer-panel__body{
      border-left: 1px solid rgba(209,216,222,1);
      border-right: 1px solid rgba(209,216,222,1);
      border-bottom: 1px solid rgba(209,216,222,1);
    }
  }
  .el-transfer__buttons{
    display: flex;
    flex-direction: column;
    row-gap: 30px;
    .el-button{
      padding: 8px;
      height: auto;
      border-radius: 3px;
    }
  }
  .el-transfer__button:nth-child(2){
    margin: 0 !important;
  }
}



// 弹窗

.el-overlay-dialog{
  overflow: hidden;
}
.el-dialog {
  max-height: 780px;
  padding: 20px;
  border-radius: 14px;
  box-shadow: 0px 2px 19px 0px rgba(169, 169, 169, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .el-dialog__header {
    padding-bottom: 33px;

    .custom-header {
      height: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-dialog__title {
        color: rgba(36, 48, 66, 1);
        font-size: 15px;
      }

      .close-btn {
        .el-icon {
          width: 20px;
          height: 20px;
          cursor: pointer;

          svg {
            height: 100%;
            width: 100%;
          }
        }
      }
    }

  }

  .el-dialog__body {
    flex: 1;
    padding-left: 20px;
    padding-right: 20px;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding-top: 40px;

    .dialog-footer {
      .el-button+.el-button {
        margin-left: 20px;
      }
    }
  }
}

// 树形控件
.el-tree{
  --el-tree-node-content-height:35px;
  --el-tree-node-hover-bg-color:rgba(255,242,232,1);
  .el-tree-node__content{
    border-radius: 6px;
    padding-right: 18px;
  }
}
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
  background-color: rgba(255,242,232,1);
}

// 徽标
.el-badge{
.el-icon{
  width: 24px;
  height: 24px;
  line-height: 24px;
  svg{
    width: 100%;
    height: 100%;
  }
}
.el-badge__content--danger{
  background-color: #FF6E6F;
}
}

// 表格
.el-table{
  --el-table-header-bg-color: rgba(247,243,240,1);
  --el-table-header-text-color: rgba(107,108,112,1);
  .cell{
    font-size: 14px;
    font-weight: normal;
  }
  .el-table__row{
    height: 50px;
  }
  .ascending .sort-caret.ascending{
    border-bottom-color: var(--el-color-warning);
  }
  .descending .sort-caret.descending{
    border-top-color: var(--el-color-warning);
  }
  th.el-table__cell{
    height: 44px;
  }
  .el-progress-bar__outer{
    height: 10px !important;
  }
}
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
  background-color: rgba(252,248,245,1);
}
.el-table__body-wrapper{
  .el-scrollbar__view{
    height: 100% !important;
  }
}
.el-table__body tr:hover {
  background: linear-gradient(90deg, rgba(252, 250, 247, 1) 0%, rgba(255, 224, 201, 1) 50.39%, rgba(252, 250, 247, 1) 100%);
}
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell{
  background: transparent;
}

// 分页
.el-pagination{
  --el-pagination-hover-color: var(--el-color-warning);
  --el-pagination-button-width: 30px;
  --el-pagination-button-height: 30px;
  .el-select .el-select__wrapper{
    min-height: 30px;
  }
  .el-input{
    height: 30px;
  }
  .el-pagination__jump,
  .el-pagination__sizes, .el-pagination__total{
    color: #333;
    font-size: 14px;
  }
}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
  background-color: rgba(255,255,255,1);
  border-radius: 4px;
}
.el-pagination.is-background .btn-next.is-active, .el-pagination.is-background .btn-prev.is-active, .el-pagination.is-background .el-pager li.is-active{
  background-color: var(--el-color-warning);
}

// 标签
.el-tag{
  height: 30px;
  min-width: 70px;
  border-radius: 6px;
  font-size: 14px;
  &.el-tag--info{
    --el-tag-text-color:#333333;
    --el-tag-bg-color:rgba(51,51,51,0.1);
    --el-tag-border-color:rgba(210,210,210,1);
  }
  &.el-tag--warning{
    --el-tag-text-color:rgba(254,105,2,1);
    --el-tag-bg-color:rgba(254,105,2,0.1);
    --el-tag-border-color:rgba(255,210,178,1);
  }
  &.el-tag--danger{
    --el-tag-text-color:rgba(245,108,108,1);
    --el-tag-bg-color:rgba(245,108,108,0.1);
    --el-tag-border-color:rgba(248,221,221,1);
  }
  &.el-tag--success{
    --el-tag-text-color:rgba(103,194,58,1);
    --el-tag-bg-color:rgba(103,194,58,0.1);
    --el-tag-border-color:rgba(208,235,194,1);
  }
  &.el-tag--primary{
    --el-tag-text-color:rgba(50,103,255,1);
    --el-tag-bg-color:rgba(50,103,255,0.1);
    --el-tag-border-color:rgba(199,214,255,1);
  }
}

// alert 提示
.el-alert{
  --el-alert-border-radius-base: 6px;
 &.el-alert--info{
  color: #333333;
 }
  &.el-alert--warning{
    background-color: #FEF1EB;
  }
  .el-alert__icon.is-big{
    align-self: baseline;
  }
  .el-alert__description{
    color: rgba(125,126,129,1) !important;
    font-size: 14px;
  }
}

// 消息提示
.el-message{
&.el-message--info{
  .el-message__content{
    color: #333333;
  }
}
&.el-message--warning{
  --el-message-bg-color:#FEF1EB;
  --el-message-border-color:#FEF1EB;
}
}

// 抽屉
.el-drawer{
  .el-drawer__header{
    height: 50px;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    background-color: rgba(187,187,187,1);
    .el-drawer__title{
      color: rgba(51,51,51,1);
      font-size: 14px;
    }
    .el-icon{
      width: 24px;
      height: 24px;
      color: rgba(16,16,16,1);
      cursor: pointer;
      font-weight: bold;
    }
  }
  .el-drawer__body{
    padding: 20px;
  }
  
}

// 消息弹框
.el-message-box{
  --el-messagebox-width: 490px;
  border-radius: 14px;
  background-color: rgba(255,255,255,1);
  color: rgba(16,16,16,1);
  font-size: 14px;
  font-family: PingFangSC-regular;
  padding: 20px;
  .el-message-box__header{
    padding: 0;
    .el-message-box__title{
      color: rgba(36,48,66,1);
      font-size: 15px;
    }
    .el-message-box__headerbtn{
      padding-top: 10px;
      padding-right: 10px;
      .el-message-box__close{
        color: #333333;
        font-weight: bold;
        font-size: 20px;
      }
    }
  }
  .el-message-box__content{
    padding-top: 40px;
    min-height: 110px;
    .el-message-box__input{
      padding-bottom: 15px;
    }
  }
  .el-message-box__btns{
    .el-button{
      &:first-child{
      @extend .el-button--default;
      }
      &.el-button--primary{
        --el-button-bg-color: var(--el-color-warning);
        --el-button-border-color: var(--el-color-warning);
        &:hover{
          background-color: var(--el-color-warning-light-7);
          border-color: var(--el-color-warning-light-7);
        }
        &:active{
          background-color: var(--el-color-warning-light-3);
          border-color: var(--el-color-warning-light-3);
        }
      }
    }

  }
}