<template>
    <div class="layout-search-dialog">
        <el-dialog v-model="state.isShow" destroy-on-close :show-close="false">
            <div class="about_box">
                <div class="layout-logo">
                    <img :src="logoMini" class="layout-logo-medium-img" />
                    <span>{{ themeConfig.globalTitle }}</span>
                </div>
                <ul>
                    <li>
                        <span>版本信息</span> <span>{{ state.info.version_information }}</span>
                    </li>
                    <li>
                        <span>版权所有</span> <span>{{ state.info.copyright_information }}</span>
                    </li>
                    <li>
                        <span>公司网址</span> <span><a @click="toHref" target="_blank" :href="state.info.company_herf">{{
                            state.info.company_website }}</a></span>
                    </li>
                    <li>
                        <span>联系电话</span> <span>{{ state.info.telephone }}</span>
                    </li>
                    <li>
                        <span>联系地址</span> <span>{{ state.info.contact_address }}</span>
                    </li>
                </ul>
                <div class="about_bottom">
                    <p>{{ state.info.copyright_number }}</p>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="info">
import { reactive, ref, nextTick, onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import logoMini from '/@/assets/favicon.png';
import { useThemeConfig } from '/@/stores/themeConfig';


// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const state = reactive<EmptyObjectType>({
    isShow: false,
    info: {
        version_information: "版本信息未获取",
        copyright_information: "版权信息未获取",
        company_herf: "未获取到地址信息",
        company_website: "公司网址未获取",
        telephone: "电话信息未获取",
        contact_address: "地址信息未获取",
        copyright_number: "版权所有信息未获取",
    }
});

// 弹窗打开
const openInfo = () => {
    state.menuQuery = '';
    state.isShow = true;
};

const toHref = ()=>{
    window.open("http://" + state.info.company_website);
};

onBeforeMount(()=>{
    state.info = themeConfig.value.info;
})

// 暴露变量
defineExpose({
    openInfo,
});
</script>

<style scoped lang="scss">
.about_box {
    ul {
        li {
            display: flex;
            justify-content: space-between;
            height: 50px;
            line-height: 50px;
            border-bottom: 1px solid #e4e4e4;
            color: #333;
            font-size: 14px;
            padding: 0 18px;
            a {
                color: #529ae8;
                cursor: pointer;
                &:hover {
                    color: #2361a3;
                }
            }
        }
    }

    .about_img {
        display: flex;
        justify-content: center;
        padding: 36px 0 50px 0;
    }

    .about_img img {
        height: 35px;
    }

    .about_bottom {
        display: flex;
        justify-content: center;
        padding: 120px 0 36px 0;
    }
}

.layout-logo {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: rgb(0 21 41 / 2%) 0px 1px 4px;
    color: var(--el-color-primary);
    font-size: 16px;
    cursor: pointer;
    animation: logoAnimation 0.3s ease-in-out;

    span {
        white-space: nowrap;
        display: inline-block;
        color: #272626;
        font-size: 20px;
        font-weight: 600;
    }

    // &:hover {
    // 	span {
    // 		color: var(--color-primary-light-2);
    // 	}
    // }

    &-medium-img {
        width: 50px;
        margin-right: 5px;
    }
}
</style>
