<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="auto">
		<div class="prompt-area">
			<p>
				<el-icon color="#fe944d"><WarningFilled /></el-icon
				>可以选择跨集群的物理机物理网卡关联，作为分布式交换机的上行链路。支持物理机上的虚拟机，在分布式交换机上的网络中按照规则通信。
			</p>
		</div>
		<el-form-item label="分布式交换机" prop="name">
			<el-input v-model="formItem.name" placeholder="请输入分布式交换机名称" />
		</el-form-item>
		<el-form-item label="物理网卡" prop="card">
			<el-input v-model="formItem.card" placeholder="请输入分布式交换机名称" v-show="false" />
			<span  @click="treeData">已选 （ {{ formItem.card.length }} ）</span>
		</el-form-item>
		<div class="ztree-publick">
			<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes" @returnOK="returnOK"></ZtreePublick>
		</div>
	</el-form>
	
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts';
import { propName } from '/@/model/resource.ts'; // 表列、正则
import { nicQuery } from '/@/api/Network'; // 接口

const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['basicOK']);
const formItem = reactive({
	name: '',
	card: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
	treeTable: [{ host_ip: '主机1',host_id:'',cluster_name:'',cluster_id:'',netName:[{name:'wk1'}],link:'',load:'',ip:'',mask:''}],
});
const propCard = (rule: any, value: any, callback: any) => {
	if (!value || value.length === 0) {
		callback(new Error('请勾选下面物理网卡'));
	} else {
		callback();
	}
}; 
const rulesForm = reactive<FormRules>({
	name: [{ validator: propName, trigger: 'change' }],
	card: [{ validator: propCard, trigger: 'change' }],
});
const transformData=(data:any)=> {
  return data.map((cluster: any) => {
    return {
      value: cluster.cluster_id,
      label: cluster.cluster_name,
      children: cluster.hosts.map((host: any) => {
        return {
          value: host.host_id,
					ip:  host.host_ip,
          label: host.host_ip,
          children: host.nics.map((nic: any) => {
            return {
              value: nic,
              label: nic,
							host_ip: host.host_ip,
							host_id: host.host_ip,
							cluster_name:  cluster.cluster_name,
							cluster_id:  cluster.cluster_id,
            };
          })
        };
      })
    };
  });
}
const convertData=(data:any)=> {
  const result:  any[] = [];

  data.forEach((cluster:any) => {
    // 添加集群信息
    result.push({
      id: cluster.cluster_id,
      name: cluster.cluster_name,
      pid: null // 集群的pid为null
    });

    cluster.hosts.forEach((host:any) => {
      // 添加主机信息
      result.push({
        id: host.host_id,
        name: host.host_ip, // 主机的name设为host_ip
        ip: host.host_ip,    // 将IP单独存储
        pid: cluster.cluster_id // 主机的pid为所属集群的id
      });
			host.nics?.forEach((nic:any) => {
        // 添加网卡信息
        result.push({
          id: nic.name,
					...(nic.is_bridge?{name: nic.name+'(已使用)'}:{name: nic.name}),
					chkDisabled: nic.is_bridge,
          pid: host.host_id, // 网卡的pid为所属主机的id
					host_ip: host.host_ip,
					host_id: host.host_id,
					cluster_name:  cluster.cluster_name,
					cluster_id:  cluster.cluster_id,
        });
      });
    });
  });

  return result;
}
const treeData = () => {
	if(!true) {
		formItem.zNodes = [
			{ id: '100', name: '集群1', pid: '' },
			{ id: '1', name: '主机1', pid: '100' },
			{ id: '4', name: '网卡1', pid: '1' },
			{ id: '5', name: '网卡2', pid: '1' },
		];
	}else {
		nicQuery({})
		.then(res => {
			// formItem.zNodes = transformData(res.data)
			formItem.zNodes = convertData(res.data)
		})
		.catch((error) => {});
	}
};
interface NetCard {
	name: string;
}
interface HostData {
	host_ip: string;
	host_id: string;
	cluster_name: string;
	cluster_id: string;
	netName: NetCard[];
	link: string;
	load: string;
	ip: string;
	mask: string;
}
const returnOK = (val: any) => {
	formItem.card = val.filter((node: any) => node.level === 2);
	const hostMap = new Map<string, HostData>(); // 用于存储主机信息，避免重复查找
	val.forEach((node: any) => {
		// 只处理网卡数据
		if (node.level == 2) {
			let host = formItem.zNodes.find((item: any) => item.id === node.pid);
			if (host) {
				let existingHost = hostMap.get(host.name);
				if (!existingHost) {
					existingHost = {
						host_ip: node.host_ip,
						host_id: node.host_id,
						cluster_name:  node.cluster_name,
						cluster_id:  node.cluster_id,
						netName: [],
						link: '',
						load: '',
						ip: '',
						mask: '',
					};
				}
				existingHost.netName = [...existingHost.netName, { name: node.name }];
				hostMap.set(host.name, existingHost);
			}
		}
	});
	const resultArray = Array.from(hostMap.values());
	formItem.treeTable = resultArray;
};

watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('basicOK', formItem);
				}
			});
		}
	}
);
setTimeout(() => {
	formItem.type = '分布式存储' + new Date();
	formItem.treeTable = []
	treeData();
}, 500);
</script>
<style lang="scss" scoped>
.prompt-area {
	margin-bottom: 20px;
	p {
		color: #ccc;
		.el-icon {
			margin-right: 5px;
		}
	}
}
.ztree-publick {
	height: 300px;
}
</style>

