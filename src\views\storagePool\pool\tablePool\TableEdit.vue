<template>
	<el-dialog v-model="formItem.isShow" append-to-body :title="formItem.title"  class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储资源名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入存储资源名称"/>
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { distributedTableEdit } from '/@/api/StoreManage'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
  title: '',
	name: '',
  id: '',
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
});
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
        formItem.isShow = false;
        distributedTableEdit({
          name: formItem.name,
          pool_id: formItem.id,
        })
        .then((res:any) => {
          if(res.msg == 'ok') {
            ElMessage.success('存储资源操作完成');
            emit('returnOK', 'refresh');
          }else {
            ElMessage.error(res.msg);
          }
        })
			}
		});
	}
};

// 打开弹窗
const editDialog = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
    formItem.title = '修改存储资源'
    formItem.name = row.name
    formItem.id = row.id
	});
};
// 暴露变量
defineExpose({
	editDialog,
});
</script>