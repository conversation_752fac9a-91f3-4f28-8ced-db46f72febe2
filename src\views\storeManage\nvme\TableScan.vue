<template>
	<el-dialog v-model="formItem.isShow" title="扫描NVME存储资源" append-to-body  class="dialog-500">
		<h3>勾选要扫描的主机</h3>
		<span>( 不选默认全部扫描 )</span>
		<div class="ztree-publick">
			<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes"></ZtreePublick>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { portGroupEdit } from '/@/api/Network'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const formItem = reactive({
	isShow: false,
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});
const treeData = () => {
	formItem.zNodes = [
		{ id: '1', name: '资源节点', pid: '0' },
		{ id: '2', name: '主机池1', pid: '1' },
		{ id: '3', name: '集群1', pid: '2' },
		{ id: '4', name: '主机1', pid: '3' },
		{ id: '5', name: '主机2', pid: '3' },
		{ id: '6', name: '主机3', pid: '3' },
		{ id: '7', name: 'vm1', pid: '4' },
		{ id: '8', name: 'vm2', pid: '4' },
		{ id: '9', name: 'vm3', pid: '5' },
		{ id: '11', name: 'vm4', pid: '5' },
		{ id: '12', name: 'vm5', pid: '5' },
		{ id: '13', name: 'vm3', pid: '5' },
		{ id: '14', name: 'vm3', pid: '5' },
		{ id: '31', name: 'aaaaa', pid: '5' },
		{ id: '32', name: 'vm4', pid: '5' },
		{ id: '63', name: 'vm2', pid: '5' },
	];
};
// 打开弹窗
const openDialog = async () => {
	formItem.isShow = true;
	nextTick(() => {
    setTimeout(() => {
			formItem.type = 'NUME-扫描'
			treeData()
		}, 200);
	});
};
const emit = defineEmits(['returnOK']);
const confirm = () => {};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style scoped lang="scss">
h3 {
	display: inline-block;
	margin-bottom: 10px;
}
.ztree-publick {
	height: 200px;
}
</style>