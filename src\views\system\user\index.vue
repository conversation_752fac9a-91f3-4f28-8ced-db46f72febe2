<template>
	<div class="system-area layout-padding">
		<el-card>
      <div class="system-content-area">
        <div class="tabs-btn-area">
          <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
          <el-button type="primary" plain @click="newClick" v-if="addJudge()">添加用户</el-button>
          <!-- <el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button> -->
        </div>
        <div class="tabs-table-area" v-if="powerItem.liebiao">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<!-- 密码有效期 -->
						<template #expiredday="{ row }">
              <span v-if="tableDisabled(row.username)">永久有效</span>
              <span v-else>{{ row.expiredday }}</span>
						</template>
            <!-- 角色 -->
						<template #role_name="{ row }">
              {{ translationRole(row.role_name) }}
						</template>
            <!-- 用户状态 -->
            <template #status="{ row }">
              <span v-if="tableDisabled(row.username)">-</span>
              <el-switch v-else v-model="row.status" inline-prompt active-value="on" inactive-value="off"  active-text="启用" inactive-text="禁用" :before-change="() => beforeChange(row)"/>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <span v-if="tableDisabled(row.username)">-</span>
							<el-dropdown trigger="click" @command="commandItem($event,row)" v-else-if="powerItem.chongzhi || powerItem.xiugai || powerItem.quanxian || powerItem.jihuo || powerItem.denglu || powerItem.ziyuanchi || powerItem.cunchuchi || powerItem.peie || powerItem.suoding || powerItem.shanchu">
							<!-- <el-dropdown trigger="click" @command="commandItem($event,row)" v-if="powerItem.chongzhi || powerItem.xiugai || powerItem.quanxian || powerItem.jihuo || powerItem.denglu || powerItem.ziyuanchi || powerItem.cunchuchi || powerItem.peie || powerItem.suoding || powerItem.shanchu"> -->
								<el-button type="primary">操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="czmm" v-if="czJudge(row.role_name)">重置密码</el-dropdown-item>
										<el-dropdown-item command="xg" v-if="powerItem.xiugai">修改用户</el-dropdown-item>
										<el-dropdown-item command="qx" v-if="qxJudge(row.role_name)">权限设置</el-dropdown-item>
										<el-dropdown-item command="jh" v-if="jhJudge(row.role_name)">激活用户</el-dropdown-item>
										<el-dropdown-item command="dl" v-if="dlJudge(row.role_name)">登录限制</el-dropdown-item>
                    <el-dropdown-item command="sd" v-if="sdJudge(row.role_name)">锁定用户</el-dropdown-item>
                    <el-dropdown-item command="jq" v-if="jqJudge(row.role_name)">集群分配</el-dropdown-item>
                    <!-- <el-dropdown-item command="zj" >主机分配</el-dropdown-item> -->
                    <el-dropdown-item command="xj" v-if="xnjJudge(row.role_name)">虚拟机分配</el-dropdown-item>
										<el-dropdown-item command="ccc" v-if="cccJudge(row.role_name)">存储池分配</el-dropdown-item>
										<el-dropdown-item command="pe" v-if="peJudge(row.role_name)">配额</el-dropdown-item>
										
										<!-- <el-dropdown-item command="fp">资源分配</el-dropdown-item> -->
										<el-dropdown-item command="sc" style="color:red" divided v-if="scJudge(row.role_name)">删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
              <span v-else>-</span>
						</template>
          </my-table>
        </div>
      </div>
		</el-card>
    <UserNew :newTime="state.newTime" @returnOK="returnOK"></UserNew>
    <UserEdit :editTime="state.editTime" :tableRow="state.tableRow" @returnOK="returnOK"></UserEdit>
    <UserPermission ref="permissionRef" />
    <UserResourePool ref="resourcePoolRef" />
    <UserStoragePool ref="storagePoolRef" />
    <UserQuota ref="quotaRef" />
    <UserAllocation ref="allocationRef" />
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts" name="User">
import { defineAsyncComponent, reactive, onMounted, h, ref, nextTick, watch } from 'vue';
import { ElMessageBox,ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
import { defaultTime } from '/@/model/logManage.ts';
import { userColumns,translationRole,tableDisabled } from '/@/model/system.ts'; // 表列、正则
import { userQuery,userStatus,userReset,userDelete } from '/@/api/System'; // 接口
const UserNew = defineAsyncComponent(() => import('./UserNew.vue'));
const UserEdit = defineAsyncComponent(() => import('./UserEdit.vue'));
const UserPermission = defineAsyncComponent(() => import('./UserPermission.vue'));
const UserResourePool = defineAsyncComponent(() => import('./UserResourePool.vue'));
const UserStoragePool = defineAsyncComponent(() => import('./UserStoragePool.vue'));
const UserQuota = defineAsyncComponent(() => import('./UserQuota.vue'));


const UserAllocation = defineAsyncComponent(() => import('./userAllocation/index.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据

const searchRef = ref();
// 定义变量内容
const state = reactive({
  columns: userColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
  tableSelect: [],
  selectRow: [],
  tableRow: {},
  newTime: '',
  editTime: '',
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  return new Promise(async(resolve)=>{
    userQuery().then((res:any)=>{
      resolve({
        data: res, // 数据
        total: res.length // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 添加用户
const newClick = ()=>{
  state.newTime = ''+new Date()
}
// 表操作列
const permissionRef = ref()
const allocationRef = ref()
const resourcePoolRef = ref()
const storagePoolRef = ref()
const quotaRef = ref()
const commandItem = (item: string,row:any)=>{
	state.tableRow = row
  switch (item) {
    case 'czmm':
			ElMessageBox.confirm(
        `是否对 <span style="font-weight: 800">${row.username}</span> 登录账号进行 重置密码 操作？`,
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
      .then(() => {
        userReset({username: row.username})
        .then(res=>{
          if(res.msg == 'ok'){
            ElMessage.success('重置密码操作完成')
          }else {
            ElMessage.error('重置密码操作失败')
          }
        })
      })
      .catch(() => {})
      break;
		case 'xg':
      state.editTime = ''+new Date()
      break;
		case 'qx':
      permissionRef.value.openDialog(row)
      break;
    case 'jh':
      console.log('激活用户')
      break;
    case 'dl':
      console.log('登录限制')
      break;
    case 'jq':
      resourcePoolRef.value.openDialog(row,'cluster')
      break;
    case 'zj':
      resourcePoolRef.value.openDialog(row,'host')
      break;
    case 'xj':
      resourcePoolRef.value.openDialog(row,'vm')
      break;
    case 'ccc':
      storagePoolRef.value.openDialog(row)
      break;
    case 'pe':
      quotaRef.value.openDialog(row)
      break;
    case 'sd':
      ElMessageBox.confirm(
        `是否对 <span style="font-weight: 800">${row.username}</span> 登录账号进行 锁定 操作？`,
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
      .then(() => {
        userReset({username: row.username})
        .then(res=>{
          if(res.msg == 'ok'){
            ElMessage.success('锁定用户操作完成')
          }else {
            ElMessage.error('锁定用户操作失败')
          }
        })
      })
      .catch(() => {})
      break;
    case 'fp':
      // allocationRef.value.openDialog(row)
      break;
		case 'sc':
      deleteClick([row])
      break;
  }
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 状态切换
const beforeChange = (row:any)=>{
  return new Promise<boolean>((resolve) => {
    let statusText = row.status=='on' ? '禁用' : '启用';
    let statusColor = row.status=='on' ? 'red' : 'green';
    ElMessageBox.confirm(
      `是否修改用户 <span style="font-weight: 800">${row.username}</span> 状态为 <span style="color:${statusColor}">${statusText}</span>？`,
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    .then(() => {
      userStatus({username:row.username,status:row.status=='on'?'off':'on'})
      .then(res=>{
        if(res.msg == 'ok'){
          ElMessage.success('切换完成')
          resolve(true); 
        }else {
          ElMessage.error('修改用户状态失败')
        }
      })
    })
    .catch(() => {})
  })
}
// 删除用户
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }
  else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '用户/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    userDelete({
      usernames: formDelet.tableNames,
      userids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除用户操作完成');
      }else {
        ElMessage.error('删除用户操作失败');
      }
    })
  }else {
    refresh()
  }
}
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
  tianjia: false,
  chongzhi: false,
  xiugai: false,
  quanxian: false,
  jihuo: false,
  denglu: false,
  jiqun: false,
  xuniji: false,
  cunchuchi: false,
  peie: false,
  suoding: false,
  shanchu: false,
});
// 添加判定
const addJudge = () => {
  // console.log(permisData.role,'---',permisData.userName)
  if(powerItem.tianjia) {
    // if(permisData.role !== 'operator'&&(permisData.userName=='sysadm'||permisData.userName=='secadm'||permisData.userName=='adtadm')) {
    if(permisData.role == 'sysadm'&&permisData.userName=='sysadm') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 重置判定
const czJudge = (iten:string)=>{
  if(powerItem.chongzhi) {
    if(permisData.role == 'secadm' &&iten=='operator') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 激活判定
const jhJudge = (iten:string)=>{
  if(powerItem.jihuo) {
    if(permisData.role == 'secadm'&&iten=='operator') {
      return true
    } else {
      return false
    }
  }else {
    return false
  }
}
// 登录判定
const dlJudge = (iten:string)=>{
  if(powerItem.denglu) {
    if(permisData.role == 'secadm'&&iten=='operator') {
      return true
    } else {
      return false
    }
  }else {
    return false
  }
}
// 锁定判定
const sdJudge = (iten:string)=>{
  if(powerItem.suoding) {
    if(permisData.role == 'secadm'&&iten=='operator') {
      return true
    } else {
      return false
    }
  }else {
    return false
  }
}
// 修改判定
const xgJudge = ()=>{
  if(powerItem.xiugai) {
    if(permisData.role == 'sysadm') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 权限判定
const qxJudge = (iten:string)=>{
  if(powerItem.quanxian) {
    if(permisData.role == 'sysadm'&&iten=='operator') {
      return false
    } else if(permisData.role == 'adtadm'&&iten=='operator')  {
      return false
    }else {
      return true
    }
  }else {
    return false
  }
}
// 集群分配
const jqJudge = (iten:string)=>{
  if(powerItem.jiqun) {
    if(permisData.role == 'sysadm'&&iten=='operator') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 虚拟机分配
const xnjJudge = (iten:string)=>{
  if(powerItem.xuniji) {
    if(permisData.role == 'sysadm'&&iten=='operator') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 存储池分配
const cccJudge = (iten:string)=>{
  if(powerItem.cunchuchi) {
    if(permisData.role == 'sysadm'&&iten=='operator') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 配额
const peJudge = (iten:string)=>{
  if(powerItem.peie) {
    if(permisData.role == 'sysadm'&&iten=='operator') {
      return true
    } else  {
      return false
    }
  }else {
    return false
  }
}
// 删除
const scJudge = (iten:string)=>{ 
  if(powerItem.shanchu) {
    // if(permisData.role == 'secadm'&&iten=='operator') {
    if(permisData.role == 'sysadm'&&iten=='operator')  {
      return true
    } else {
      return false
    }
  }else {
    return false
  }
}

import { powerCodeQuery } from '/@/api/System'; // 权限
import { fa, tr } from 'element-plus/es/locale';

const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'yonghuliebiao',
    'tianjiayonghu',
    'chongzhimima',
    'xiugaiyonghu',
    'quanxianshezhi',
    'jihuoyonghu',
    'dengluxianzhi',
    'jiqunfenpei',
    'xunijifenpei',
    'cunchuchifenpei',
    'peie',
    'suoding',
    'shanchuyonghu',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.yonghuliebiao;
		powerItem.tianjia = res.data.tianjiayonghu;
    powerItem.chongzhi = res.data.chongzhimima;
		powerItem.xiugai = res.data.xiugaiyonghu;
		powerItem.quanxian = res.data.quanxianshezhi;
		powerItem.jihuo = res.data.jihuoyonghu;
    powerItem.denglu = res.data.dengluxianzhi;
    powerItem.jiqun = res.data.jiqunfenpei;
    powerItem.xuniji = res.data.xunijifenpei;
    powerItem.cunchuchi = res.data.cunchuchifenpei;
    powerItem.peie = res.data.peie;
    powerItem.suoding = res.data.suoding;
		powerItem.shanchu = res.data.shanchuyonghu;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style scoped lang="scss">
.system-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .system-content-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>
