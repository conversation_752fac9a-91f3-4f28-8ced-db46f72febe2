<template>
	<div class="monitoring-area layout-padding">
    <el-card>
      <div class="physics-area">
        <!-- 物理机基本数据 -->
        <div class="physics-basic">
          <el-card v-for="item in state.basicData" :key="item.ip" class="physics-basic-item">
            <div class="basic-item-title">
              <h4>{{item.title}}（{{item.ip}}）</h4>
              <span :class="machineState('color',item.status)">
                {{ machineState('text',item.status)}}
              </span>
            </div>
            <div class="basic-item-img">
              <img v-if="item.status=='operation'" src="../../assets/images/gj-run.png">
              <img v-if="item.status=='fault'" src="../../assets/images/gj-fault.png">
              <img v-if="item.status=='shutdown'" src="../../assets/images/gj-shutdown.png">
              <img v-if="item.status=='alarm'" src="../../assets/images/gj-alarm.png">
              <img v-if="item.status=='-'" src="../../assets/images/no-data.svg" >
            </div>
            <div class="basic-item-progress">
              <div class="progress-rate">
                <span>CPU总数</span>
                <span>{{item.status=="operation"?item.cpuTatal+" 核":"-"}}</span>
              </div>
              <div class="progress-rate">
                <span>物理内存总数</span>
                <span>{{item.status=="operation"?formatFileSize(item.memoryTatal):"-"}}</span>
              </div>
              <div class="progress-rate">
                <span>CPU使用率</span>
                <span>{{item.status=="operation"?item.cpuUsageRate+" %":"-"}}</span>
              </div>
              <el-progress :color="['#98c4fa', '#0176f6']" :stroke-width="15" :text-inside="true" :percentage="item.cpuUsageRate>99?99:item.cpuUsageRate" />
              <div class="progress-rate">
                <span>物理内存使用率</span>
                <span>{{item.status=="operation"?item.memoryUsageRate+" %":"-"}}</span>
              </div>
              <el-progress :color="['#98c4fa', '#0176f6']" :stroke-width="15" :text-inside="true" :percentage="item.memoryUsageRate>99?99:item.memoryUsageRate" />
            </div>
          </el-card>
        </div>
        <!-- 物理机图表 -->
        <div class="physics-chart">
          <div class="radio-chart-select">
            <div class="chart-radio">
              <div class="radio-chart-piece" v-for="item in state.trendData" :key="item.label" @click="radioClick(item.lable)">
                <span class="radio-title" :style="{color:state.trendItem==item.lable?'#121529':'#717379'}">{{item.title}}</span>
                <span class="radio-selected" :style="{background:state.trendItem==item.lable?'#fe6902':''}"></span>
              </div>
            </div>
            <el-select v-model="state.time" style="width:250px"  @change="timeChange">
              <el-option label="最近1小时" :value="1" />
              <el-option label="最近24小时" :value="24" />
              <el-option label="最近1个月" :value="720" />
            </el-select>
          </div>
          <div class="chart-content">
            <div class="chart-no-datas" v-if="!state.chartShwo">
              <img src="../../assets/images/no-data.svg" >
            </div>
            <EchartLine :chartData="state.chartData" :time="state.time" v-if="state.chartShwo"></EchartLine>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { selectTime,formatFileSize } from '/@/model/monitoring.ts'; // 表列、正则
import { physicsBasicQuery,physicsCpuQuery,physicsMemQuery,physicsDiskQuery,physicsNetQuery } from '/@/api/Monitoring'; // 接口

const EchartLine = defineAsyncComponent(() => import('./echartPublic/EchartLine.vue'));

// 定义变量内容
const state = reactive({
	basicData: [{
    title:"无数据",
    ip:"-",
    status:"-", // 运行
    cpuTatal: 0, // 分配比
    memoryTatal: 0, // 分配比
    cpuUsageRate: 0,  // 使用率
    memoryUsageRate: 0, // 使用率
  }],
  trendData: [
    { title:"CPU趋势",lable:"cpu" },
    { title:"内存趋势",lable:"memory" },
    { title:"磁盘趋势",lable:"disk" },
    { title:"网络趋势",lable:"network" },
  ],
  trendItem: 'cpu',
  time: 1,
  chartData: {},
  chartShwo: false,
});
// 机器状态
const machineState = (type:any,status:any)=>{
  let text = '未检测到数据'
  let clas = 'machine-close'
  switch (status) {
    case 'operation':
      text = '机器运行'
      clas = 'machine-run'
      break;
    case 'fault':
      text = '机器错误'
      clas = 'machine-fault'
      break;
    case 'shutdown':
      text = '机器关闭'
      clas = 'machine-close'
      break;
    case 'alarm':
      text = '机器告警'
      clas = 'machine-prompt'
      break;
  }
  if(type == 'color') {
    return clas
  }else {
    return text
  }
}
// 基本数据查询
const hostBasicQuery = ()=>{
  physicsBasicQuery().then(res=>{
    let arr = new Array()
    res.forEach((em:any) => {
      arr.push({
        title:em.hostname,
        ip:em.ip,
        status:em.status=="enabled"?"operation":"shutdown",
        cpuTatal:em.vcpus,
        memoryTatal:em.memory_mb,
        cpuUsageRate:progressJudge(em.status,em.cpu_percent),
        memoryUsageRate:progressJudge(em.status,em.memory_mb_used/em.memory_mb*100),
      })
    });
    state.basicData = arr
  })
}
// 时间选择
const timeChange =(item:any)=>{
  switch (state.trendItem) {
    case 'cpu':
      cpuQuery()
      break;
    case 'memory':
      memQuery()
      break;
    case 'disk':
      diskQuery()
      break;
    case 'network':
      netQuery()
      break;
  }
}
// 趋势切换
const radioClick = (item:any)=>{
  state.trendItem = item
  timeChange(state.time)
}
// 获取CPU图表数据
const cpuQuery = ()=>{
  physicsCpuQuery({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
    if(res.data?.length>0) {
      state.chartData = res
      state.chartShwo = true
    }else {
      state.chartShwo = false
    }
  })
}
// 获取内存图表数据
const memQuery = ()=>{
  physicsMemQuery({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
    if(res.data?.length>0) {
      state.chartData = res
      state.chartShwo = true
    }else {
      state.chartShwo = false
    }
  })
}
// 获取磁盘图表数据
const diskQuery = ()=>{
  physicsDiskQuery({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
    if(res.data?.length>0) {
      state.chartData = res
      state.chartShwo = true
    }else {
      state.chartShwo = false
    }
  })
}
// 获取网络图表数据
const netQuery = ()=>{
  physicsNetQuery({start:selectTime(state.time),end:selectTime(0)}).then(res=>{
    if(res.data?.length>0) {
      state.chartData = res
      state.chartShwo = true
    }else {
      state.chartShwo = false
    }
  })
}
// 进度条
const progressJudge = (status:string,size:any)=>{
  if(status == 'enabled'&&size) {
    return parseFloat(size.toFixed(1))
  }else {
    return 0
  }
}
onMounted(() => {
  hostBasicQuery()
  timeChange(state.time)
})
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .physics-area {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .physics-basic {
      width: 100%;
      height: 320px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      overflow: auto;
      .physics-basic-item {
        width: 350px;
        height: 300px;
        margin-bottom: 10px;
        .basic-item-title {
          display: flex;
          justify-content: space-between;
        }
        .basic-item-img {
          img {
            width: 150px;
            height: 100px;
          }
          display: flex;
          justify-content: center;
        }
        .basic-item-progress {
          height: calc(100% - 125px);
          overflow: auto;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .progress-rate {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
    .physics-chart {
      width: 100%;
      height: 400px;
      .radio-chart-select {
        display: flex;
        justify-content: space-between;
        height: 50px;
        .chart-radio {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 15px;
          .radio-chart-piece {
            display: flex;
            align-content: space-between;
            flex-direction: column;
            align-items: center;
            margin-right: 26px;
            font-weight: 800;
            cursor: pointer;
            .radio-selected {
              width: 10px;
              height: 6px;
              border-radius: 3px;
            }
          }
        }
      }
      .chart-content {
        width:100%;
        height:calc(100% - 50px);
        .chart-no-datas {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            height: 70%;
          }
        }
      }
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
.machine-run {
  color: #01cfc7;
}
.machine-fault {
  color: #ff6966;
}
.machine-close {
  color: #9ea3b0;
}
.machine-prompt {
  color: #fe9e1f;
}
</style>