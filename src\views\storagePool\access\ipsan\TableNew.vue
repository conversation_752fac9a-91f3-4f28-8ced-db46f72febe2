<template>
	<el-dialog v-model="formItem.isShow" title="添加IPsan存储池" append-to-body class="dialog-800">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储资源">
				<el-input v-model="formItem.deviceName" disabled />
			</el-form-item>
			<el-form-item label="存储池名称" prop="name">
				<el-input v-model="formItem.name" placeholder="请输入端口组名称" />
			</el-form-item>
			<el-form-item label="使用方式">
				<el-radio-group v-model="formItem.way">
					<el-radio value="share">共享存储</el-radio>
					<el-radio value="iscsi">ISCSI存储</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="OCFS2心跳网络" v-if="formItem.way == 'share'">
				<el-radio-group v-model="formItem.ocfs2">
					<el-radio value="mange">管理网络</el-radio>
					<el-radio value="storage">存储网络</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="分布式交换机" prop="switch" v-if="formItem.ocfs2 == 'storage'">
				<el-select v-model="formItem.switch" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3" placeholder="请选择分布式交换机">
					<el-option v-for="item in formItem.switchData" :key="item.id" :label="item.name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="已关联主机">
				<div class="hosts-area">
					<my-table ref="hostsRef" :pagination="formItem.pagination" :columns="[
						{ prop: 'cluster_name', label: '集群' },
						{ prop: 'hostname', label: '物理机' },
						{ prop: 'ip', label: '主机IP' },
					]" :request="getTableHosts">
					</my-table>
				</div>
			</el-form-item>
			<!-- <el-form-item label="选择主机" prop="selectNodes">
				<div class="ztree-publick">
					<ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes" @returnOK="returnOK"></ZtreePublick>
				</div>
			</el-form-item> -->
			<el-form-item label="已选存储资源" prop="devicePath">
				<el-input v-model="formItem.devicePath" disabled />
			</el-form-item>
		</el-form>
		<div class="table-area">
			<my-table ref="tableRef" :pagination="formItem.pagination" :columns="formItem.columns" :request="getTableData">
				<!-- 展开行内容 -->
				<template #expandContent="{ row }">
					<div class="expand-content">
						<span v-if="row.ip_san_devices.length == 0">未登录</span>
						<el-radio-group v-model="formItem.iqnID" v-else>
							<el-radio v-for="item in row.ip_san_devices" :value="item.id" size="large" @change="radioChange(item)">{{
								item.device_path
							}}</el-radio>
						</el-radio-group>
					</div>
				</template>
				<!-- 登录状态 -->
				<template #login_status="{ row }">
					<el-button v-if="row.ip_san_devices.length == 0" @click="loginClick(row)">登录</el-button>
					<el-tag v-else type="success">已登录</el-tag>
				</template>
			</my-table>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { ipTableTarget, ipTableSwitch, ipTargetLogin, ipTableCreate } from '/@/api/StoreManage/index.ts'; // 接口
import { resourceTreeQuery } from '/@/api/ResourcePool'; // 接口
import { propName } from '/@/model/network.ts'; // 表列、正则
import { iscsiSorageColumns } from '/@/model/storeManage.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	columns: iscsiSorageColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	tableSearch: '',
	deviceName: '',
	id: '',
	name: '',
	way: 'share',
	ocfs2: 'mange',
	switch: '',
	devicePath: '',
	targetID: '',
	switchData: [{ name: '主机1', id: '1111111' }],
	selectNodes: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
	iqnID: '',
	lists: false,
	hosts: [],
});
// const poolName = (rule: any, value: any, callback: any) => {
// 	const regex = /^[a-zA-Z0-9@_.-]{2,32}$/;
// 	if (!regex.test(value)) {
// 		callback(new Error('2-32 个英文、数字、特殊字符@_.-'));
// 	} else {
// 		callback();
// 	}
// };
const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项', trigger: 'blur' },
		{ validator: propName, trigger: 'change' },
		// { validator: poolName, trigger: 'change' },
	],
	switch: [{ required: true, message: '必选项', trigger: 'change' }],
	devicePath: [{ required: true, message: '必选项', trigger: 'change' }],
	selectNodes: [{ required: true, message: '必选项', trigger: 'change' }],
});
const treeData = () => {
	if (!true) {
		formItem.zNodes = [
			{ id: '1', name: '资源节点', pid: '0' },
			{ id: '2', name: '主机池1', pid: '1' },
			{ id: '3', name: '集群1', pid: '2' },
			{ id: '4', name: '主机1', pid: '3' },
			{ id: '5', name: '主机2', pid: '3' },
			{ id: '6', name: '主机3', pid: '3' },
		];
	} else {
		resourceTreeQuery()
			.then((res) => {
				formItem.zNodes = res.data;
			})
			.catch((error) => {});
	}
};
// 树返回
const returnOK = (val: any) => {
	formItem.selectNodes = val.filter((node: any) => node.level === 3);
};
// 打开弹窗
const openDialog = async (row: any) => {
	formItem.isShow = true;
	nextTick(() => {
		formItem.hosts = row.hosts;
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
			formItem.deviceName = row.device_name;
			formItem.id = row.id;
			formItem.iqnID = '';
			if (tableRef.value) {
				refresh();
			}
		}
		setTimeout(() => {
			formItem.type = 'IPsan-添加' + new Date();
			// hostColnyQuery()
			treeData();
		}, 200);
	});
};
const hostsRef =  ref();
const tableRef = ref();
// 刷新
const refresh = () => {
	hostsRef.value.handleSearch(); // 收索事件 表1页
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// 分布式交换机
const hostColnyQuery = () => {
	formItem.switchData = [];
	let hostData: any[] = [];
	ipTableSwitch().then((res: any) => {
		res?.forEach((em: any) => {
			hostData.push({
				name: em.name,
				id: em.id,
			});
		});
		formItem.switchData = hostData;
	});
};
// 获取主机数据
const getTableHosts = (params: EmptyObjectType, page: EmptyObjectType) => {
	return {
		data: formItem.hosts, // 数据
		total: formItem.hosts.length, // 总数
	};
}
// 获取表数据
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	if (formItem.lists) {
		return {
			data: [
				{ target_name: '测试1', ip_san_devices: [{ device_path: 'aaaa', id: '1111111' }], id: 'l11' },
				{ target_name: '测试1', id: '222', ip_san_devices: [] },
				{
					target_name: '测试2',
					ip_san_devices: [
						{ device_path: 'bbbbb', id: '222222' },
						{ device_path: 'ccccc', id: '333333' },
					],
					id: '333',
				},
			], // 数据
			total: 1, // 总数
		};
	} else {
		return new Promise(async (resolve) => {
			ipTableTarget({
				id: formItem.id,
				page: page.pageNum, // 当前页
				pagecount: page.pageSize, // 每页条
				order_type: page.order, // 排序规则
				order_by: page.sort, // 排序列
				search_str: formItem.tableSearch, // 搜索条件
			})
				.then((res: any) => {
					resolve({
						data: res.data, // 数据
						total: res.total, // 总数
					});
				})
				.catch((err: any) => {
					resolve({
						data: [], // 数据
						total: 0, // 总数
					});
				});
		});
	}
};
// 表格单选
const radioChange = (value: any) => {
	formItem.devicePath = value.device_path;
	formItem.targetID = value.ip_san_target_id;
};
// 表格登录
const loginClick = (row: any) => {
	ipTargetLogin({
		id: row.id,
	}).then((res: any) => {
		ElMessage.success('登录成功');
		refresh();
	});
};
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				ipTableCreate({
					device_id: formItem.id,
					name: formItem.name,
					storage_type: formItem.way,
					...(formItem.way === 'share' ? { ocfs2: formItem.ocfs2 } :null),
					// hosts: formItem.hosts,
					target_id: formItem.targetID,
					device_path: formItem.devicePath,
				}).then((res:any) => {
					if (res.msg == 'ok') {
						ElMessage.success(res.message);
						emit('returnOK', 'refresh');
					}else {
						ElMessage.error(res.msg);
					}
				});
			}
		});
	}
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.hosts-area {
	height: 100px;
	width: 100%;
	position: relative;
}
.table-area {
	height: 200px;
	width: 100%;
	position: relative;
}
.expand-content {
	padding: 0 50px;
}
.ztree-publick {
	height: 200px;
}
</style>