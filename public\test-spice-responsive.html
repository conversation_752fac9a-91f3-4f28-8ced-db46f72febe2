<!DOCTYPE html>
<html>
<head>
    <title>SPICE 响应式尺寸测试</title>
    <link rel="stylesheet" type="text/css" href="spice-html5/spice.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            width: 80%;
            height: 600px;
            border: 2px solid #333;
            margin: 20px auto;
            position: relative;
            background: #f0f0f0;
        }
        .info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        #spice-area {
            width: 100%;
            height: 100%;
            position: relative;
        }
        .spice-screen {
            width: 100% !important;
            height: 100% !important;
            background: #000;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            margin: 0 10px;
            padding: 10px 20px;
            font-size: 14px;
        }
        .size-display {
            margin: 10px 0;
            padding: 10px;
            background: #e0e0e0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>SPICE HTML5 客户端响应式尺寸测试</h1>
    
    <div class="size-display">
        <strong>当前尺寸信息：</strong>
        <div id="size-info">等待连接...</div>
    </div>
    
    <div class="controls">
        <button onclick="testConnect()">模拟连接</button>
        <button onclick="resizeContainer(800, 600)">调整为 800x600</button>
        <button onclick="resizeContainer(1200, 800)">调整为 1200x800</button>
        <button onclick="resizeContainer('50%', '400px')">调整为 50% x 400px</button>
        <button onclick="toggleFullscreen()">切换全屏</button>
    </div>
    
    <div class="container" id="test-container">
        <div class="info">
            容器尺寸: <span id="container-size">800x600</span><br>
            Canvas 尺寸: <span id="canvas-size">未创建</span><br>
            屏幕元素尺寸: <span id="screen-size">未创建</span>
        </div>
        
        <div id="spice-area">
            <div id="spice-screen" class="spice-screen">
                <div style="color: white; text-align: center; padding-top: 50px;">
                    点击"模拟连接"按钮创建 SPICE 画布
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟 SPICE 连接和 canvas 创建
        function testConnect() {
            const spiceScreen = document.getElementById('spice-screen');
            
            // 清空现有内容
            spiceScreen.innerHTML = '';
            
            // 创建模拟的 canvas（模拟 SPICE 客户端行为）
            const canvas = document.createElement('canvas');
            
            // 模拟原始的固定尺寸设置（720x400）
            canvas.setAttribute('width', '720');
            canvas.setAttribute('height', '400');
            canvas.setAttribute('id', 'spice_surface_0');
            canvas.setAttribute('tabindex', '0');
            
            // 应用我们的响应式样式修复，确保撑满容器
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.minWidth = '100%';
            canvas.style.minHeight = '100%';
            canvas.style.maxWidth = '100%';
            canvas.style.maxHeight = '100%';
            canvas.style.objectFit = 'fill'; // 填充整个容器
            canvas.style.display = 'block';
            canvas.style.margin = '0';
            canvas.style.padding = '0';
            canvas.style.border = 'none';
            canvas.style.boxSizing = 'border-box';
            canvas.style.cursor = 'none';
            
            // 绘制一些测试内容
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, 720, 400);
            
            ctx.fillStyle = '#3498db';
            ctx.fillRect(50, 50, 620, 300);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('SPICE 响应式测试', 360, 200);
            ctx.fillText('Canvas: 720x400 → 100% 响应式', 360, 240);
            
            // 应用我们的屏幕元素样式修复，确保撑满容器
            spiceScreen.style.width = '100%';
            spiceScreen.style.height = '100%';
            spiceScreen.style.minWidth = '100%';
            spiceScreen.style.minHeight = '100%';
            spiceScreen.style.maxWidth = '100%';
            spiceScreen.style.maxHeight = '100%';
            spiceScreen.style.margin = '0';
            spiceScreen.style.padding = '0';
            spiceScreen.style.border = 'none';
            spiceScreen.style.boxSizing = 'border-box';
            spiceScreen.style.display = 'block';
            spiceScreen.style.overflow = 'hidden';
            
            spiceScreen.appendChild(canvas);
            
            updateSizeInfo();
            console.log('✅ 模拟 SPICE 连接完成，Canvas 已创建');
        }
        
        function resizeContainer(width, height) {
            const container = document.getElementById('test-container');
            container.style.width = typeof width === 'string' ? width : width + 'px';
            container.style.height = typeof height === 'string' ? height : height + 'px';
            
            setTimeout(updateSizeInfo, 100);
        }
        
        function toggleFullscreen() {
            const container = document.getElementById('test-container');
            if (container.style.position === 'fixed') {
                // 退出全屏
                container.style.position = 'relative';
                container.style.top = 'auto';
                container.style.left = 'auto';
                container.style.width = '80%';
                container.style.height = '600px';
                container.style.zIndex = 'auto';
            } else {
                // 进入全屏
                container.style.position = 'fixed';
                container.style.top = '0';
                container.style.left = '0';
                container.style.width = '100vw';
                container.style.height = '100vh';
                container.style.zIndex = '9999';
            }
            
            setTimeout(updateSizeInfo, 100);
        }
        
        function updateSizeInfo() {
            const container = document.getElementById('test-container');
            const spiceScreen = document.getElementById('spice-screen');
            const canvas = document.getElementById('spice_surface_0');
            
            // 更新容器尺寸显示
            document.getElementById('container-size').textContent = 
                `${container.clientWidth}x${container.clientHeight}`;
            
            // 更新屏幕元素尺寸显示
            if (spiceScreen) {
                document.getElementById('screen-size').textContent = 
                    `${spiceScreen.clientWidth}x${spiceScreen.clientHeight}`;
            }
            
            // 更新 Canvas 尺寸显示
            if (canvas) {
                const rect = canvas.getBoundingClientRect();
                document.getElementById('canvas-size').textContent = 
                    `显示: ${Math.round(rect.width)}x${Math.round(rect.height)} | 内部: ${canvas.width}x${canvas.height}`;
            }
            
            // 更新详细信息
            const sizeInfo = document.getElementById('size-info');
            sizeInfo.innerHTML = `
                <strong>容器:</strong> ${container.clientWidth}x${container.clientHeight}<br>
                <strong>spice-screen:</strong> ${spiceScreen ? spiceScreen.clientWidth + 'x' + spiceScreen.clientHeight : '未创建'}<br>
                <strong>Canvas 显示尺寸:</strong> ${canvas ? Math.round(canvas.getBoundingClientRect().width) + 'x' + Math.round(canvas.getBoundingClientRect().height) : '未创建'}<br>
                <strong>Canvas 内部尺寸:</strong> ${canvas ? canvas.width + 'x' + canvas.height : '未创建'}
            `;
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(updateSizeInfo, 100);
        });
        
        // 初始化尺寸信息
        setTimeout(updateSizeInfo, 100);
    </script>
</body>
</html>
