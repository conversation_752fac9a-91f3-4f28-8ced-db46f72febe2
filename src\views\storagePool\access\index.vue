<template>
	<div>
		<el-menu :default-active="state.active" class="el-menu-vertical-demo" @select="onClose">
			<el-menu-item index="IPsan" v-if="powerItem.IPsan">
				<template #title>
					<span>IPsan</span>
				</template>
			</el-menu-item>
			<el-menu-item index="FCsan" v-if="powerItem.FCsan">
				<template #title>
					<span>FCsan</span>
				</template>
			</el-menu-item>
			<el-menu-item index="NAS" v-if="powerItem.NAS">
				<template #title>
					<span>NAS</span>
				</template>
			</el-menu-item>
			<el-menu-item index="NVME" v-if="powerItem.NVME">
				<template #title>
					<span>NVME</span>
				</template>
			</el-menu-item>
			<el-menu-item index="distributed" v-if="powerItem.fenbushicunchu">
				<template #title>
					<span>分布式存储</span>
				</template>
			</el-menu-item>
		</el-menu>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref,watch } from 'vue';
// 定义变量内容
const state = reactive({
	active: 'IPsan',
});
const emit = defineEmits(['accessOK']);
const onClose = (item: string) => {
  emit('accessOK', item);
}
// 页面加载时
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	IPsan: false,
	FCsan: false,
	NAS: false,
	NVME: false,
	fenbushicunchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:['IPsan','FCsan','NAS','NVME','fenbushicunchu']}).then((res:any)=>{
		powerItem.IPsan = res.data.IPsan;
		powerItem.FCsan = res.data.FCsan;
		powerItem.NAS = res.data.NAS;
		powerItem.NVME = res.data.NVME;
		powerItem.fenbushicunchu = res.data.fenbushicunchu;
		if(powerItem.IPsan){
			state.active = 'IPsan';
			emit('accessOK', 'IPsan');
		}else if(powerItem.FCsan){ 
			state.active = 'FCsan';
			emit('accessOK', 'FCsan');
		}else if(powerItem.NAS){ 
			state.active = 'NAS';
			emit('accessOK', 'NAS');
		}else if(powerItem.NVME){
			state.active = 'NVME';
			emit('accessOK', 'NVME');
		}else if(powerItem.fenbushicunchu){
			state.active = 'fenbushicunchu';
			emit('accessOK', 'fenbushicunchu');
		}else {
			state.active = 'null';
			emit('accessOK', 'null');
		}
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
// onMounted(() => {
//   emit('accessOK', 'IPsan');
// });
</script>
<style scoped lang="scss">
.el-menu-vertical-demo {
	width: 100%;
	.el-menu-item.is-active {
		color: var(--next-color-white) !important;
	}
	.el-menu-item {
		margin-bottom: 20px;
	}
	.el-menu-hover-bg-color, .el-menu-item:hover, .el-menu-item.is-active, .el-sub-menu.is-active .el-sub-menu__title, .el-sub-menu:not(.is-opened):hover .el-sub-menu__title {
		// background: var(--el-menu-active-color)!important;
		color: var(--next-bg-menuBarActiveColor)!important;
	}
}
</style>
