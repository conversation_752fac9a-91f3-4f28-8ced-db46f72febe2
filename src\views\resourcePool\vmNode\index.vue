<template>
	<div class="tabs-item-area">
		<div class="tabs-item-btn">
			<el-button type="primary" v-if="permisData.xunijikaiji &&state.status == '关机'" round @click="actionClick('开机')">开机</el-button>
			<el-button type="primary" v-if="permisData.xunijiguanji &&state.status !== '关机'" round @click="actionClick('关机')">关机</el-button>
			<el-button type="primary" v-if="permisData.xunijichongqi" round @click="actionClick('重启')">重启</el-button>
			<el-button type="primary" v-if="permisData.xunijiqiangzhichongqi" round @click="actionClick('强制重启')">强制重启</el-button>
			<el-button type="primary" v-if="permisData.xunijiguanbidianyuan" round @click="actionClick('关闭电源')">关闭电源</el-button>
			<el-button type="primary" v-if="permisData.xunijizanting&&state.status == '开机'" round @click="actionClick('暂停')">暂停</el-button>
			<el-button type="primary" v-if="permisData.xunijihuifu&&state.status == '暂停'" round @click="actionClick('恢复')">恢复</el-button>
			<el-button type="primary" v-if="permisData.xunijixiugai" round @click="editClick">修改</el-button>
			<el-button type="primary" v-if="permisData.xunijikongzhitai&&state.status == '开机'" round @click="consoleClick">控制台</el-button>
			<el-button type="primary" v-if="permisData.xunijishanchu" round @click="deleteClick">删除</el-button>
			<el-dropdown trigger="click" @command="groupOperation" v-if="permisData.xunijiwanquankelong ||permisData.xunijilianjiekelong ||permisData.xunijiqianyi">
				<el-button type="primary" round style="margin-left: 10px">
					更多<el-icon class="el-icon--right"><ArrowDownBold /></el-icon>
				</el-button>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item command="wqkl" v-if="permisData.xunijiwanquankelong">完全克隆</el-dropdown-item>
						<el-dropdown-item command="ljkl" v-if="permisData.xunijilianjiekelong">链接克隆</el-dropdown-item>
						<el-dropdown-item command="qy" v-if="permisData.xunijiqianyi">迁移</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
			<span @click="statusQuery">当前状态：{{ state.status }}--点击刷新</span>
		</div>
		<div class="route-list">
			<div v-for="item in state.routeList" v-show="item.code"  :class="{ 'route-item': true, 'is-active': isActive(item.name) }" :key="item.name" @click="tagClick(item.name)">
				<span
					><span>{{ item.name }}</span></span
				>
			</div>
		</div>
		<div class="tabs-item-center">
			<VirtualMachineSummary v-if="state.acive == '概要'" :treeItem="props.treeItem" :acive="state.acive" />
			<Backups v-if="state.acive == '虚拟机备份'" :treeItem="props.treeItem" :acive="state.acive" />
			<Snapshot v-if="state.acive == '虚拟机快照'" :treeItem="props.treeItem" :acive="state.acive" />
			<OperationLog v-if="state.acive == '运行日志'" :treeItem="props.treeItem" :acive="state.acive" />
			<MigrationLog v-if="state.acive == '虚拟机迁移日志'" :treeItem="props.treeItem" :acive="state.acive" />
			<Monitor v-if="state.acive == '性能监控'" :treeItem="props.treeItem" :acive="state.acive" />
			<Alarm v-if="state.acive == '告警'" :treeItem="props.treeItem" :acive="state.acive" />
			<Task v-if="state.acive == '任务'" :treeItem="props.treeItem" :acive="state.acive" />
			<Console v-if="state.acive == '控制台'" :treeItem="props.treeItem" :acive="state.acive" />
		</div>
    <VMedit  ref="editRef" @returnOK="returnOK"/>
    <VMgeneral  ref="generalRef" @returnOK="returnOK"/>
    <VmDelete :deleteTime="state.deleteTime" :treeItem="props.treeItem" @deleteOK="deleteOK"></VmDelete>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { dayjs, ElMessage } from 'element-plus';
import { vmOverview } from '/@/api/ResourcePool'; // 接口
import { recycleVmDelete,vmMoveinRecycle } from '/@/api/ResourcePool/vm'; // 接口

const VirtualMachineSummary = defineAsyncComponent(() => import('./VirtualMachineSummary.vue'));
const Backups = defineAsyncComponent(() => import('./backups/index.vue'));
const Snapshot = defineAsyncComponent(() => import('./snapshot/index.vue'));
const OperationLog = defineAsyncComponent(() => import('./operationLog/index.vue'));
const MigrationLog = defineAsyncComponent(() => import('./migrationLog/index.vue'));
const Monitor = defineAsyncComponent(() => import('./monitor/index.vue'));
const Alarm = defineAsyncComponent(() => import('./alarm/index.vue'));
const Task = defineAsyncComponent(() => import('./task/index.vue'));
const Console = defineAsyncComponent(() => import('./console/index.vue'));

const VMedit = defineAsyncComponent(() => import('/@/views/resourcePool/resourcePublic/vmOperate/VMedit.vue'));
const VMgeneral = defineAsyncComponent(() => import('/@/views/resourcePool/resourcePublic/vmOperate/VMgeneral.vue'));
const VmDelete = defineAsyncComponent(() => import('./VmDelete.vue'));
import { permissionStores } from '/@/stores/permission'; // 权限数据
const permisData = permissionStores(); // 权限数据

const props = defineProps({
	treeItem: {
		type: Object,
		required: true,
	},
});
const state = reactive({
	routeList: [
    { name: '概要', code: permisData.xunijigaiyao },
    { name: '虚拟机备份', code: permisData.xunijibeifen },
    { name: '虚拟机快照', code: permisData.xunijikuaizhao },
    { name: '运行日志', code: permisData.yunxingrizhi },
    { name: '虚拟机迁移日志', code: permisData.qianyirizhi },
    { name: '性能监控', code: permisData.xingnengjiankong },
    { name: '告警', code: permisData.gaojing },
    { name: '任务', code: permisData.renwu },
    { name: '控制台', code: permisData.xunijikongzhitai },
  ],
	acive: '概要',
	status: '',
	deleteTime: '',
	hostIP: '',
	spicePort: 0,
});
const statusQuery = () => {
	vmOverview(props.treeItem.id).then((res) => {
		// let libs = ['开机', '关机', '暂停', '关闭电源','休眠'];
		// state.status = libs[Math.round(Math.random() * 4)];
		state.status = statusTransition(res.data.status);
		state.hostIP = res.data.host_ip;
		state.spicePort = res.data.spice_port;
	});
};
const statusTransition=(item:string)=>{
	let list = '开机'
	switch(item){
		case 'running':
			list = '开机'
		break;
		case 'stop':
			list = '关机'
		break;
		case 'pause':
			list = '暂停'
		break;
		case 'poweroff':
			list = '关闭电源'
		break;
		case 'hibernate':
			list = '休眠'
		break;
		default:
			list = item
	}
	return list
}
const generalRef = ref(); // 通用操作
const actionClick = (item: string) => {
	let color = 'green'
	if(item === '开机'){
		color = 'green'
	}else if(item === '关机'){
		color = 'red'
	}else if(item === '暂停'){
		color = 'red'
	}else if(item === '恢复'){
		color = 'green'
	}else if(item === '关闭电源'){
		color = 'red'
	}else if(item === '重启'){
		color = 'red'
	}
  generalRef.value.openDialog(item,color,[props.treeItem]);
};
const editRef = ref();
const editClick = ()=>{
	editRef.value.openDialog(props.treeItem);
}
const tagClick = (v: string) => {
	state.acive = v;
};
const isActive = (v: string) => {
	if (state.acive === v) {
		return true;
	} else {
		return false;
	}
};
// 表格群操作
const groupOperation = (item: string) => {
	switch (item) {
		case 'wqkl':
     	generalRef.value.openDialog('完全克隆','green',[props.treeItem]);
			break;
		case 'ljkl':
     	generalRef.value.openDialog('链接克隆','green',[props.treeItem]);
			break;
		case 'qy':
			console.log('迁移');
			break;
		default:
			console.log('其他');
	}
};
const emit = defineEmits(['returnOK']);
const returnOK = (item: string) => {
	emit('returnOK', item);
};
// 删除
const deleteClick = () => {
  state.deleteTime = '虚拟机/'+new Date()
};
const consoleClick = () => {
	const spiceUrl = `/spice-html5/spice.html?list=${state.hostIP}&port=${state.spicePort}`
  if(state.spicePort!== 0) {
		window.open(spiceUrl, '_blank')
	}
}
// 删除返回
const deleteOK = (item:string)=>{
  if(item == 'delete'){
    recycleVmDelete({
      names: [props.treeItem.name],
      ids: [props.treeItem.id],
    })
    .then(res => {
      if(res.msg == 'ok'){
        ElMessage.success('彻底删除虚拟机操作完成');
				emit('returnOK', 'delete');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }else {
    vmMoveinRecycle({
      names: [props.treeItem.name],
      ids: [props.treeItem.id],
    })
    .then(res => {
      if(res.msg == 'ok'){
        ElMessage.success('删除虚拟机移入回收站操作完成');
        emit('returnOK', 'delete');
      }else {
        ElMessage.error(res.msg);
      }
    })
  }
}
watch(
	() => props.treeItem,
	(val) => {
		powerJudge();
		statusQuery();
	}
);
const powerJudge = ()=>{
  console.log('资源节点-list')
  const firstAvailableRoute = state.routeList.find(item => item.code === true);
  if (firstAvailableRoute) {
    state.acive = firstAvailableRoute.name;
  }
}
// 页面加载时
onMounted(() => {
  powerJudge()
	statusQuery();

});
</script>
<style lang="scss" scoped>
.tabs-item-area {
	width: calc(100%);
	height: calc(100%);
	.route-list {
		width: calc(100% - 40px);
		height: 55px;
		// background: var(--el-fill-color-blank);
		background: #faf7f7;
		border-radius: 26px;
		margin: 10px;
		padding: 0 20px;
		display: flex;
		flex-wrap: wrap;
		align-items: center;

		.route-item {
			position: relative;
			padding: 0 20px;
			font-size: 14px;
			line-height: 50px;
			cursor: pointer;
			margin: 0 10px;
			color: var(--el-color-title);
			border-radius: 3px;
			display: flex;
			height: 75%;
			align-items: center;

			&:hover {
				background: var(--el-color-primary-light-9);
				font-weight: bold;
				color: var(--el-color-primary);
				&::before {
					content: ' ';
					position: absolute;
					width: 4px;
					height: 18px;
					top: 50%;
					transform: translateY(-50%);
					background: var(--el-color-primary);
					left: 0;
				}
			}
		}

		.is-active {
			// background: var(--el-color-primary-light-9);
			background: #fff9f5;
			font-weight: bold;
			color: var(--el-color-primary);
			&::before {
				content: ' ';
				position: absolute;
				width: 4px;
				height: 18px;
				top: 50%;
				transform: translateY(-50%);
				background: var(--el-color-primary);
				left: 0;
			}
		}
	}
	.tabs-item-center {
		padding: 10px;
		border-radius: 10px;
		width: calc(100%);
		height: calc(100% - 110px);
		background: var(--el-fill-color-blank);
	}
}
</style>
