<template>
	<div class="layout-parent">
		<div class="route-list">
			<div v-for="(v,k) in state.routeList" class="route-area" :key="k">
				<span v-if="v.meta?.isType"  class="route-type">▷{{v.meta?.isType}}</span>
				<div :class="{ 'route-item': true, 'is-active': isActive(v) }" v-show="!isHides(v)" :key="v.routePath" @click="tagClick(v)">
					<span
						><span>{{ setTagsViewNameI18n(v) }}</span></span
					>
				</div>
			</div>
			
		</div>
		<div class="route-content">
			<router-view></router-view>
		</div>
	</div>
</template>

<script setup lang="ts" name="layoutParentView">
import { defineAsyncComponent, computed, reactive, onBeforeMount, onUnmounted, nextTick, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useThemeConfig } from '/@/stores/themeConfig';
import mittBus from '/@/utils/mitt';
import other from '/@/utils/other';
import router from '/@/router';

// 定义变量内容
const route = useRoute();
const state = reactive<ParentViewState>({
	refreshRouterViewKey: '', // 非 iframe tagsview 右键菜单刷新时
	iframeRefreshKey: '', // iframe tagsview 右键菜单刷新时
	keepAliveNameList: [],
	iframeList: [],
	routeList: [],
	routeActive: '',
	routePath: '',
});

const isActive = (v: RouteItem) => {
	if ((v.query && Object.keys(v.query).length) || (v.params && Object.keys(v.params).length)) {
		// 普通传参
		return v.url ? v.url === state.routeActive : v.path === state.routeActive;
	} else {
		return v.path === state.routePath;
	}
};
const isHides = (v: RouteItem) => {
	const isHide = v.meta?.isHide;
	return typeof isHide === 'function' ? isHide() : isHide;
}
const tagClick = (v: RouteItem) => {
	state.routePath = v.path;
	router.push(v.path);
};

// 设置 自定义 tagsView 名称、 自定义 tagsView 名称国际化
const setTagsViewNameI18n = computed(() => {
	return (v: RouteItem) => {
		return other.setTagsViewNameI18n(v);
	};
});

// 获取子路由列表
const getChildRoutes = () => {
	const parentRouteRecord = route.matched[route.matched.length - 2];
	state.routePath = route.path;
	state.routeList = parentRouteRecord.children;
};
// 页面加载前，处理缓存，页面刷新时路由缓存处理
onBeforeMount(() => {});
// 页面加载时
onMounted(() => {
	getChildRoutes();
});
// 页面卸载时
onUnmounted(() => {
	mittBus.off('onTagsViewRefreshRouterView', () => {});
});
// 监听路由变化，防止 tagsView 多标签时，切换动画消失
// https://toscode.gitee.com/lyt-top/vue-next-admin/pulls/38/files
watch(
	() => route.fullPath,
	() => {
		state.refreshRouterViewKey = decodeURI(route.fullPath);
	},
	{
		immediate: true,
	}
);
</script>
<style lang="scss" scoped>
.route-list {
	width: calc(100% - 20px);
	height: 55px;
	background: var(--el-fill-color-blank);
	border-radius: 26px;
	margin: 10px;
	padding: 0 20px;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	.route-area {
		display: flex;
    align-items: center;
		.route-type {
			color: #888585;
			padding: 0 10px 0 20px
		}
	}
	.route-item {
		position: relative;
		padding: 0 20px;
		font-size: 14px;
		line-height: 50px;
		cursor: pointer;
		margin: 0 10px;
		color: var(--el-color-title);
		border-radius: 3px;
		display: flex;
		height: 75%;
		align-items: center;

		&:hover {
			background: var(--el-color-primary-light-9);
			font-weight: bold;
			color: var(--el-color-primary);
			&::before {
				content: ' ';
				position: absolute;
				width: 4px;
				height: 18px;
				top: 50%;
				transform: translateY(-50%);
				background: var(--el-color-primary);
				left: 0;
			}
		}
	}

	.is-active {
		// background: var(--el-color-primary-light-9);
		background: #fff9f5;
		font-weight: bold;
		color: var(--el-color-primary);
		&::before {
			content: ' ';
			position: absolute;
			width: 4px;
			height: 18px;
			top: 50%;
			transform: translateY(-50%);
			background: var(--el-color-primary);
			left: 0;
		}
	}
}

.route-content {
	width: calc(100%);
	height: calc(100% - 80px);
	position: relative;
}
</style>