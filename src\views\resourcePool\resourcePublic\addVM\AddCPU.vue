<template>
	<el-form ref="formRef" label-position="left" :model="formCPU" :rules="rulessForm" label-width="150">
    <el-form-item prop="vcpus">
      <template #label>
        <div class="vm-new-label" @click="formCPU.typeShow = !formCPU.typeShow">
          <el-icon><ArrowDown v-show="formCPU.typeShow" /><ArrowRight v-show="!formCPU.typeShow" /></el-icon>
          <span>CPU总数</span>
        </div>
      </template>
      <el-input v-model="formCPU.vcpus" type="number" />
    </el-form-item>
    <div v-show="formCPU.typeShow">
      <el-form-item label="主CPU">
        <el-input v-model="formCPU.cores" :max="48" :min="2" type="number" />
      </el-form-item>
      <el-form-item label="CPU最大值">
        <el-input v-model="formCPU.cpuMax" :max="48" :min="2" type="number" />
      </el-form-item>
      <el-form-item label="CPU架构">
        <el-select v-model="formCPU.framework" style="width: 100%">
          <el-option label="64 位" value="64" />
          <el-option label="32 位" value="32" />
        </el-select>
      </el-form-item>
      <el-form-item label="CPU工作模式">
        <el-select v-model="formCPU.mode" style="width: 100%">
          <el-option label="兼容" value="jr" />
          <el-option label="主机匹配" value="zj" />
          <el-option label="直通" value="zt" />
        </el-select>
      </el-form-item>
      <el-form-item label="CPU预留(%)">
        <el-input v-model="formCPU.reserve" :max="48" :min="2" type="number" />
      </el-form-item>
      <el-form-item label="CPU预留(%)">
        <el-input v-model="formCPU.limit" :max="48" :min="2" type="number" />
      </el-form-item>
	  </div>
  </el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'

const props = defineProps({
  cpuTime: {
    type: String,
    required: true
  }
});
const formRef = ref<FormInstance>()
const emit = defineEmits(['cpuOK']);
// 硬件信息-CPU
const formCPU = reactive({
  typeShow: false,
  vcpus: 2,
  cores: 48,
  cpuMax: 2,
  framework: '64',
  mode: 'jr',
  reserve: 2,
  limit: 2,
})
const propCPU = (rule:any, value:any, callback:any) => {
  const regex = /^(?:[1-9]|[1-5]\d|60)$/
  if (!regex.test(value)) {
    callback(new Error("请输 1 - 60 之间的CPU数量"))
  } else {
    callback()
  }
}
const rulessForm = reactive<FormRules>({
  vcpus: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propCPU, trigger: "change" },
  ],
})

watch(
  ()=> props.cpuTime,
  (val)=>{
    if (formRef.value) {
      formRef.value.validate(val=>{
        if (val) {
          emit('cpuOK', formCPU);
        }else {
          emit('cpuOK', false);
        }
      })
    }
  }
);

</script>
<style lang="scss" scoped>
  .vm-new-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    >span{
      padding-left: 10px;
    }
  }
</style>