<template>
	<div class="layout-navbars-breadcrumb">
		<div class="breadcrumb-icon" v-if="state.breadcrumbList.length">
			<img src="/breadcrumb.jpg" />
		</div>
		<el-breadcrumb class="layout-navbars-breadcrumb-main" separator="/">
			<el-breadcrumb-item v-for="(v, k) in state.breadcrumbList" :key="!v.meta.tagsViewName ? v.meta.title : v.meta.tagsViewName">
				<span v-if="k === state.breadcrumbList.length - 1" class="layout-navbars-breadcrumb-span">
					<div v-if="!v.meta.tagsViewName">{{ $t(v.meta.title) }}</div>
					<div v-else>{{ v.meta.tagsViewName }}</div>
				</span>
				<a v-else @click.prevent="onBreadcrumbClick(v)">
					{{ $t(v.meta.title) }}
				</a>
			</el-breadcrumb-item>
		</el-breadcrumb>
	</div>
</template>
<script setup lang="ts" name="layoutBreadcrumb">
import { reactive, onMounted } from 'vue';
import { RouteLocationNormalized, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
import other from '/@/utils/other';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useRoutesList } from '/@/stores/routesList';

// 定义变量内容
const stores = useRoutesList();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { routesList } = storeToRefs(stores);
const route = useRoute();
const router = useRouter();
// eslint-disable-next-line no-undef
const state = reactive<BreadcrumbState>({
	breadcrumbList: [],
	routeSplit: [],
	routeSplitFirst: '',
	routeSplitIndex: 1,
	routePath: '',
});

// 面包屑点击时
// eslint-disable-next-line no-undef
const onBreadcrumbClick = (v: RouteItem) => {
	const { redirect, path } = v;
	if (redirect) router.push(redirect);
	else router.push(path);
};

// 处理面包屑数据
// eslint-disable-next-line no-undef
const getBreadcrumbList = (arr: RouteItems, index: number) => {
	if (index <= 0) {
		for (let i = 0; i < arr.length; i++) {
			const item = arr[i];
			if (item.path === state.routePath) {
				state.breadcrumbList.push(item);
				return;
			}
		}
	}
	// eslint-disable-next-line no-undef
	arr.forEach((item: RouteItem) => {
		state.routeSplit.forEach((v: string, k: number, arrs: string[]) => {
			if (state.routeSplitFirst === item.path || state.routeSplitFirst + '/' + v === item.path) {
				state.routeSplitFirst += `/${arrs[state.routeSplitIndex]}`;
				state.breadcrumbList.push(item);
				state.routeSplitIndex++;
				if (item.children) getBreadcrumbList(item.children, index + 1);
			}
		});
	});
};
// 当前路由字符串切割成数组，并删除第一项空内容
const initRouteSplit = (routeItem: RouteLocationNormalized) => {
	if (!themeConfig.value.isBreadcrumb) return false;
	const path = routeItem.path;
	state.routePath = path;
	state.breadcrumbList = [];
	state.routeSplit = path.split('/');
	state.routeSplit.shift();
	state.routeSplitFirst = ``;
	state.routeSplitIndex = 0;
	getBreadcrumbList(routesList.value, 0);
	// state.breadcrumbList.push(routeItem);
	if ((route.name === 'notFound' && state.breadcrumbList.length > 1)) state.breadcrumbList.shift();
	if (state.breadcrumbList.length > 0)
		// eslint-disable-next-line no-undef
		state.breadcrumbList[state.breadcrumbList.length - 1].meta.tagsViewName = other.setTagsViewNameI18n(<RouteToFrom>routeItem);
};
// 页面加载时
onMounted(() => {
	initRouteSplit(route);
});
// 路由更新时
onBeforeRouteUpdate((to) => {
	initRouteSplit(to);
});
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb {
	height: 52px;
  flex-shrink: 0;
	min-width: 1166px;
	display: flex;
	align-items: center;
	padding-left: 30px;
	line-height: 52px;
	background: #fff;
	color: var(--el-color-black);
	:deep(.el-breadcrumb) {
		font-size: 16px;
		font-weight: 700;
	}
	.layout-navbars-breadcrumb-icon {
		cursor: pointer;
		font-size: 18px;
		color: #000;
		height: 100%;
		width: 40px;
		opacity: 0.8;
		&:hover {
			opacity: 1;
		}
	}

	.layout-navbars-breadcrumb-span {
		display: flex;
		align-items: center;
		opacity: 0.7;
		color: #161616;
		font-weight: 700 !important;
	}

	.layout-navbars-breadcrumb-iconfont {
		font-size: 14px;
		margin-right: 5px;
	}

	:deep(.el-breadcrumb__separator) {
		opacity: 0.7;
		color: #dadada;
	}

	:deep(.el-breadcrumb__inner a, .el-breadcrumb__inner.is-link) {
		font-weight: unset !important;
		color: #dadada;
		text-decoration: none;
		&:hover {
			color: var(--el-color-primary) !important;
		}
	}
}
.breadcrumb-icon {
	display: flex;
	align-items: center;
	width: 20px;
	height: 20px;
	margin-right: 5px;
	img {
		width: 100%;
		height: 100%;
	}
}
</style>
