<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<div class="prompt-area">
			<p><el-icon color="#fe944d"><WarningFilled /></el-icon>交换机VLAN ID 用于支持多台物理机之间的通信。若交换机已用于存储网络或集群HA心跳网络，则不允许修改“VLAN ID、类型、移除物理机或网卡、修改IP、掩码、链路聚合模式”，请先调整存储网络或关闭集群HA，再调整交换机相关配置。</p>
		</div>
		<el-form-item label="交换机VLAN ID" prop="vlanID">
      <el-input v-model="formItem.vlanID" type="number" :min=1 placeholder="有效值1-4096"/>
		</el-form-item>
		<el-form-item label="网络类型" prop="type">
			<el-select v-model="formItem.type" style="width: 100%">
				<el-option label="存储网络" value="cc" />
				<el-option label="业务网络" value="yw" />
				<el-option label="管理网络" value="gl" />
			</el-select>
		</el-form-item>
		<el-form-item label="网络配置" @click="ceshi">
		</el-form-item>
		<div class="net-config-area">
			<el-table :data="formItem.tableData">
				<!-- 物理机名称 -->
				<el-table-column prop="hostName" label="物理机名称" />
				<!-- 网卡名称 -->
				<el-table-column label="网卡名称">
					<template #default="scope">
						<span>{{ names(scope.row.netName) }}</span>
					</template>
				</el-table-column>
				<!-- 链路聚合模式 -->
				<el-table-column label="链路聚合模式">
					<template #default="scope">
						<el-select v-model="scope.row.link" style="width: 100%">
							<el-option label="静态" value="jt" />
							<el-option label="动态" value="dt" />
						</el-select>
					</template>
				</el-table-column>
				<!-- 负载模式 -->
				<el-table-column label="负载模式">
					<template #default="scope">
						<el-select v-model="scope.row.load" style="width: 100%">
							<el-option label="主备负载" value="zb" />
							<el-option label="基本负载" value="jb" />
							<el-option label="高级负载分组" value="gj" />
						</el-select>
					</template>
				</el-table-column>
				<!-- IP -->
				<el-table-column label="IP" v-if="formItem.type!=='yw'">
					<template #default="scope">
						<el-input v-model="scope.row.ip" placeholder="请输入"/>
					</template>
				</el-table-column>
				<!-- 子网掩码 -->
				<el-table-column label="子网掩码" v-if="formItem.type!=='yw'">
					<template #default="scope">
          	<el-input v-model="scope.row.mask" placeholder="请输入"/>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts';
import { propVlan } from '/@/model/network.ts'; // 表列、正则
const props = defineProps({
	times: {
		type: String,
		required: true,
	},
});
const formRef = ref<FormInstance>();
const emit = defineEmits(['netOK']);
const formItem = reactive({
	vlanID: '1',
	type: 'cc',
	tableData:[{
		hostName: '虚拟交换机',
		netName: [{ name: '网卡1' }, { name: '网卡2' }],
		link: '',
		load: '',
		ip: '',
		mask: '',
	}],
});
const rulesForm = reactive<FormRules>({
	vlanID: [
    { required: true, message: '必填项' },
    { validator: propVlan, trigger: "blur" }
  ],
  type: [{ required: true, message: '必选项', trigger: 'blur' }],
});
// 拆分网卡名
const names=(row:any)=>{
	return row.map((item:any)=>{
		return item.name
	}).toString()
}
const ceshi = () => {
}


watch(
	() => props.times,
	(val) => {
		if (formRef.value) {
			formRef.value.validate((val) => {
				if (val) {
					emit('netOK', formItem);
				}
			});
		}
	}
);
</script>
<style lang="scss" scoped>
.prompt-area {
  margin-bottom: 20px;
  p {
    color: #ccc;
    .el-icon {
      margin-right: 5px;
    }
  }
}
.net-config-area {
	width: 100%;
	height: 300px;
	position: relative;
}
</style>
