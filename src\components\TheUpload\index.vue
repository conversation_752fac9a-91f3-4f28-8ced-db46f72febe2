<template>
  <div class="upload-container">
    <div class="upload-header" v-if="showHeader">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
    </div>
    
    <el-upload
      class="upload-area"
      drag
      :action="action"
      :auto-upload="autoUpload"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :file-list="fileList"
      :limit="limit"
      :on-exceed="handleExceed"
      :multiple="multiple"
      :accept="accept"
      :disabled="disabled"
      :show-file-list="false"
      v-bind="$attrs"
    >
      <div class="upload-drop-area">
        <el-icon :size="40"><plus /></el-icon>
        <div class="el-upload__text">
          <p v-html="enhancedDropText"></p>
          <p v-if="currentFile" class="upload-description">当前文件加载{{ currentFile.percentage }}%</p>
          <p v-else class="upload-description">{{ tipText }}</p>
        </div>
      </div>
      
      <template #tip>
        <div class="el-upload__tip">
          <slot name="tip"></slot>
        </div>
      </template>
    </el-upload>

    <div class="file-list" v-if="showFileList">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <div class="file-icon">
          <el-icon v-if="file.raw && file.raw.type.includes('word')"><document /></el-icon>
          <el-icon v-else-if="file.raw && file.raw.type.includes('sheet')"><document /></el-icon>
          <el-icon v-else-if="file.raw && file.raw.type.includes('pdf')"><document /></el-icon>
          <el-icon v-else><document /></el-icon>
        </div>
        <div class="file-info">
          <div class="file-name">{{ file.name }}</div>
          <div class="file-meta">{{ formatFileSize(file.size) }} {{ formatDate(file.uid) }}</div>
        </div>
        <div class="file-status">
          <template v-if="file.status === 'uploading'">
            <div class="progress-bar">
              <div class="progress" :style="{width: file.percentage + '%'}"></div>
            </div>
            <span class="upload-text" @click="cancelUpload(file)">取消上传</span>
          </template>
          <template v-else-if="file.status === 'success'">
            <span class="success-text">上传成功</span>
          </template>
          <template v-else>
            <span class="error-text" @click="handleFileRemove(file)">删除</span>
          </template>
        </div>
      </div>
    </div>
    
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import { Plus, Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 基本设置
  title: {
    type: String,
    default: 'Upload 上传'
  },
  description: {
    type: String,
    default: '点击或拖放文件到此区域，即可上传文件。'
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  
  // 上传相关
  action: {
    type: String,
    default: '#'
  },
  autoUpload: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: ''
  },
  limit: {
    type: Number,
    default: 3
  },
  disabled: {
    type: Boolean,
    default: false
  },
  
  // 文件列表相关
  modelValue: {
    type: Array,
    default: () => []
  },
  showFileList: {
    type: Boolean,
    default: true
  },
  
  // 文本提示
  dropText: {
    type: String,
    default: '将文件拖到此处，或<em>点击上传</em>'
  },
  tipText: {
    type: String,
    default: '支持 .docx、.pdf、.xlsx等格式，单个文件不超过200MB'
  },
  exceedText: {
    type: String,
    default: '最多只能上传 {limit} 个文件，本次选择了 {files} 个文件，共超出 {exceed} 个文件'
  }
})

const emit = defineEmits([
  'update:modelValue',
  'change',
  'remove',
  'exceed',
  'success',
  'error',
  'progress',
  'cancel'
])

const currentFile = ref(null)
const fileList = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleFileChange = (file, files) => {
  currentFile.value = {
    percentage: Math.floor(Math.random() * 100)
  }
  emit('change', file, files)
}

const handleFileRemove = (file) => {
  emit('remove', file)
}

const cancelUpload = (file) => {
  emit('cancel', file)
}

const handleExceed = (files) => {
  const text = props.exceedText
    .replace('{limit}', props.limit)
    .replace('{files}', files.length)
    .replace('{exceed}', files.length + fileList.value.length - props.limit)
  
  ElMessage.warning(text)
  emit('exceed', files)
}

const formatFileSize = (size) => {
  if (!size) return '0B'
  
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + 'MB'
  }
}

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(parseInt(timestamp))
  return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')}`
}

// 处理dropText中的<em>标签
const enhancedDropText = computed(() => {
  return props.dropText.replace(/<em>(.*?)<\/em>/g, '<em>$1</em>')
})
</script>

<style scoped>
.upload-container {
  width: 100%;
}

.upload-header {
  margin-bottom: 20px;
}

.upload-header h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

.upload-header p {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.upload-area {
  width: 100%;
  margin-bottom: 20px;
}

.upload-drop-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--el-text-color-regular);
  padding: 40px 20px;
  text-align: center;
}

.upload-description {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 10px;
}

.el-upload__text {
  margin-top: 10px;
}

.el-upload__text em {
  color: var(--el-color-primary);
  font-style: normal;
}

.file-list {
  margin-top: 20px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 10px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.file-icon {
  font-size: 24px;
  margin-right: 10px;
  color: var(--el-text-color-secondary);
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.file-status {
  display: flex;
  align-items: center;
}

.progress-bar {
  width: 100px;
  height: 6px;
  background-color: var(--el-fill-color);
  border-radius: 3px;
  margin-right: 10px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: var(--el-color-primary);
}

.upload-text {
  font-size: 12px;
  color: var(--el-text-color-regular);
  cursor: pointer;
}

.success-text {
  font-size: 12px;
  color: var(--el-color-success);
}

.error-text {
  font-size: 12px;
  color: var(--el-color-danger);
  cursor: pointer;
}
</style> 