html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden; /* 防止滚动条 */
}
body
{
    /* background-color: #999999; */
    background: url(./imgs/background.jpg)no-repeat center center;
    background-attachment:fixed;
    background-size:cover;
    color: #000000; 
    margin: 0;
    padding: 0;
    font-family: "Lucida Grande", "Lucida Sans Unicode", "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
    font-size: 12pt;
    line-height: 1.5em;
}

* { margin: 0; }

button
{
    margin-bottom: 15px;
    cursor: pointer;
    color: #000000;
    background-color: #999999;
    border: 1px solid #4a4a4a;
    min-width: 150px;
    border-radius: 10px;
    background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#d55b34));
    background: -moz-linear-gradient(top, #fff, #d55b34);
    -ms-transform: translate(15%, -8%);
    transform: translate(15%, -8%);
    font-size: .90em;
    font-weight: bolder;
    padding: 2px 0px 3px 2px;
    /* padding: 0px 0px 3px 0px; */
    /* margin-top: 5px; */
}
button:focus
{
    outline: none;
}
button:hover
{
    background-color: #24414e
}
button:active
{
    background-color: #24414e;
    transform: translate(14.85%, -7%);
}

.SidenavClosed
{
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #bbbbbb;
    box-shadow: 1px 0px #00000040;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
}
.SidenavOpen
{
    height: 100%;
    width: 200px !important;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    /* background-color: #bbbbbb; */
    background: rgb(82 80 80 / 65%);
    box-shadow: 1px 0px #00000040;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
}
#Sidenav label
{ 
    color: #000000;
    margin-left: 3%;
    text-shadow: 1px 1px 0px rgba(175, 210, 220, 0.8);
    position: absolute;
    font-size: .9em;
}
#Sidenav input
{
    padding: 3px;
    background-color: #fAfAfA;
    border: 1px inset #999999;
    outline: none;
    float: right;
    margin-right: 3%;
    -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    position: relative;
    max-width: 110px;
}
#Sidenav .closebtn
{
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 25px;
    /* margin-left: 50px; */
    cursor: pointer;
    color: #000000;
}
.titleNav {
    position: absolute;
    top: 10px;
    color: #f0f0f0;
    width: 85%;
    text-align: center;
    font-size: 22px;
    text-shadow: 1px 1px 4.5px #ff6565;
}
.hideGateway {
    /* display: none; */
}
canvas
{
    cursor: none;
    /* 确保 canvas 响应式缩放并撑满容器 */
    width: 100% !important;
    height: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: fill; /* 填充整个容器 */
    display: block;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important; /* 移除边框避免尺寸计算问题 */
    box-sizing: border-box !important;
}

#login
{
    position: fixed;
    background: #6060607d;
    z-index: 1;
    padding-top: 2px;
    border-bottom: 2px solid #00000040;
    width: 100%;

}
#login p
{
    margin-left: 27%;
    display: inline;
    font-size: large;
}

#spice-area
{
    height: 100% !important;
    width: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
    overflow: hidden !important; /* 防止内容溢出产生滚动条 */
    display: block !important;
    position: relative !important;
}
.spice-screen
{
    width: 100% !important;
    height: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
    overflow: hidden !important; /* 防止内容溢出 */
    position: relative !important;
    display: block !important;
}
.spice-message
{
    width: 90%;
    height: 40%;
    overflow: auto;
    margin-left: auto;
    margin-right: auto;
    padding: 10px;
    font-size: 8pt;
    line-height: 1.1em;
    font-family: 'Andale Mono', monospace;
    background-color: #fAfAfA;
    border: 1px inset #999999;
    border-radius: 3px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
.spice-message p {
    margin-bottom: 0em;
    margin-top: 0em;
}
.spice-message-warning {
    color: orange;
}
.spice-message-error {
    color: red;
}

.slecUSB {
    width: 134px;
    margin-bottom: 15px;
    border: 1px solid #4a4a4a;
    color: #000000;
    min-width: 134px;
    background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#d55b34));
    border-radius: 10px;
    transform: translate(14%, -8%);
    font-weight: bolder;
    font-size: .90em;
    padding: 0 8px;
}
#emptyBox {
    width: 350px;
    position: fixed;
} 