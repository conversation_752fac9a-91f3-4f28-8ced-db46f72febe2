import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { directive } from '/@/directive/index';
import { i18n } from '/@/i18n/index';
import other from '/@/utils/other';
// import $ from 'jquery';
import "/@/components/ztree/js/jquery-3.2.1.min";
import "/@/components/ztree/js/jquery.ztree.core.js";
import "/@/components/ztree/js/jquery.ztree.excheck";
import "/@/components/ztree/js/jquery.ztree.exedit";
import "/@/components/ztree/css/zTreeStyle/zTreeStyle.css";

import ElementPlus from 'element-plus';
// @ts-ignore
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import '/@/theme/index.scss';
import VueGridLayout from 'vue-grid-layout';
import 'amfe-flexible';
import 'dayjs/locale/zh-cn'

import '/@/theme/iconfont/font_4971620_uskxdz2z2t/iconfont.js'

const app = createApp(App);

directive(app);
other.elSvg(app);

app.use(pinia).use(router).use(ElementPlus, {
  locale: zhCn
}).use(i18n).use(VueGridLayout).mount('#app');
