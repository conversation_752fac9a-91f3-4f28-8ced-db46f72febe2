<template>
<div>
	<el-form ref="formRef" label-position="left" label-width="150" :model="formModel">
		<div v-for="(item, index) in netData" :key="'net' + index">
			<el-form-item :prop="'net' + index" :rules="[netRules(index)]">
				<template #label>
					<div class="vm-new-label" @click="item.typeShow = !item.typeShow">
						<el-icon><ArrowDown v-show="item.typeShow" /><ArrowRight v-show="!item.typeShow" /></el-icon>
						<span>{{ index == 0 ? '网卡' : '网卡' + index }}</span>
					</div>
				</template>
				<el-input v-model="item.net" disabled>
					<template #append>
						<el-button @click="selectNet(index)" :icon="Search" />
						<el-button v-if="item.remove" @click="removeNet(index)" :icon="Delete" style="margin-left: 20px" />
					</template>
				</el-input>
			</el-form-item>
			<div v-show="item.typeShow">
				<el-form-item label="网卡类型">
					<el-select v-model="item.netType" style="width: 100%">
						<el-option label="高速网卡" value="high" />
						<el-option label="普通网卡" value="ordinary" />
						<el-option label="Intel e1000网卡" value="e1000" />
						<el-option label="SE-IOV网卡" value="iov" />
					</el-select>
				</el-form-item>
				<el-form-item label="内核加速" v-if="item.netType == 'high'">
					<el-switch v-model="item.accelerate" inline-prompt active-text="开启" inactive-text="关闭" />
				</el-form-item>
				<el-form-item label="VF网卡" v-if="item.netType == 'iov'">
					<el-select v-model="item.netVF" style="width: 100%">
						<el-option label="未知" value="high" />
					</el-select>
				</el-form-item>
				<el-form-item label="IP 地址">
					<el-input v-model="item.ip" placeholder="请输入IP地址" />
				</el-form-item>
				<!-- <el-form-item label="MAC地址" :prop="'mac' + index" :rules="[macRules(index)]"> -->
				<el-form-item label="MAC地址">
					<el-input v-model="item.mac" placeholder="请输入MAC地址" />
				</el-form-item>
				<el-form-item label="网卡多队列" v-if="item.netType == 'high'">
					<el-switch v-model="item.queue" inline-prompt active-text="开启" inactive-text="关闭" />
				</el-form-item>
			</div>
		</div>
	</el-form>
	<SelectNet :netDialog="formData.netDialog" :treeItem="props.treeItem" @net-return="netReturn"></SelectNet>
</div>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';
const SelectNet = defineAsyncComponent(() => import('./SelectNet.vue'));

const props = defineProps({
	treeItem: {
    type: Object,
    required: true
  },
	netAdd: {
		type: Number,
		required: true,
	},
	netTime: {
		type: String,
		required: true,
	},
});
const formData = reactive({
	netDialog: '',
	netIndex: 0,
})
const formModel = ref({}); // 可以动态填充你的表单数据
const formRef = ref<FormInstance>();
const emit = defineEmits(['netOK']);
// 硬件信息-网卡
let netData = reactive([
  {
    typeShow: false,
    remove: false,
    net: '',
    netID: '',
		switchID: '',
    netType: 'high',
    accelerate: true,
		ip: '',
    mac: '',
    queue: false,
    netVF: '',
  }
])
// 选择网卡
const selectNet = (item: any)=>{
	formData.netDialog = " " +new Date()
	formData.netIndex = item
}
// 选择网卡返回
const netReturn = (item: any)=>{
  netData[formData.netIndex].net = item.tableName
  netData[formData.netIndex].netID = item.tableID
  netData[formData.netIndex].switchID = item.switchID
}
// 判断网卡选择
const netRules = (index: number) => {
	return {
		validator: (rule: any, value: any, callback: any) => {
			if (netData[index].net=="") {
				callback(new Error('未选择网卡'));
			} else {
				callback();
			}
		},
		trigger: 'change', // 校验触发时机
	};
};
// 判断MAC地址
const macRules = (index: number) => {
	const regex = /^([0-9A-Fa-f]{2}[:.-]?){5}([0-9A-Fa-f]{2})$/;
	return {
		validator: (rule: any, value: any, callback: any) => {
			if (!regex.test(netData[index].mac)) {
				callback(new Error('请输入正确的MAC地址'));
			} else {
				callback();
			}
		},
		trigger: 'change', // 校验触发时机
	};
};
watch(
	() => props.netTime,
	async (val) => {
		if (formRef.value) {
			try {
				// 执行表单验证
				await formRef.value.validate();
				// 如果验证通过，触发事件
				emit('netOK', netData); // 取消注释以触发事件
			} catch (error) {
				// 如果验证失败，标记失败项并输出错误
				netData.forEach((item, index) => {
					// 这里可以增加对特定字段的验证，标记 `typeShow` 为 `true`
					emit('netOK', false)
					item.typeShow = true;
				});
			}
		}
	}
);
watch(
	() => props.netAdd,
	(val) => {
		netData.push({
      typeShow: false,
      remove: true,
      net: '',
      netID: '',
			switchID: '',
      netType: 'high',
      accelerate: true,
			ip: '',
      mac: '',
      queue: false,
      netVF: '',
    })
	}
);
// 删除网卡
const removeNet = (item: any)=>{
  netData.splice(item,1)
}
</script>
<style lang="scss" scoped>
.vm-new-label {
	display: flex;
	align-items: center;
	cursor: pointer;
	> span {
		padding-left: 10px;
	}
}
</style>