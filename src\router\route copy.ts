import { RouteRecordRaw } from 'vue-router';
import { powerCodeQuery } from '/@/api/System'; // 接口
import { reactive } from 'vue';

const formItem = reactive({
	gailan: false,
	ziyuan<PERSON>: false,
	cunchuguanli: false,
	wangluoguan<PERSON>: false,
	jiankonggaojing: false,
	xitonggongneng: false,

	fenbushijiaohuanji: false,
	anquanzu: false,
	wulijijiankong: false,
	xunijijiankong: false,
	gaojingguanli: false,
	gaojingguize: false,
	yonghuguanli: false,
	rizhi: false,
	shouquan: false,
	xiugaimima: false,
	anquanpeizhi: false,
	xitongguanli: false,
});
const permission = () => {
	powerCodeQuery({
			module_code:[
				'gailan','ziyuanchi','cunchuguanli','wangluoguanli','jiankonggaojing','xitonggongneng',
				'fenbushijiaohuanji','anquanzu','wulijijiankong','xunijijiankong','gaojingguan<PERSON>','gaojingguize','yonghuguan<PERSON>','rizhi','shouquan','xiugaimima','anquanpeizhi','xitongguanli'
			]
		}).then((res:any)=>{
			formItem.gailan = res.data.gailan
			formItem.ziyuanchi = res.data.ziyuanchi
			formItem.cunchuguanli = res.data.cunchuguanli
			formItem.wangluoguanli = res.data.wangluoguanli
			formItem.jiankonggaojing = res.data.jiankonggaojing
			formItem.xitonggongneng = res.data.xitonggongneng

			formItem.fenbushijiaohuanji = res.data.fenbushijiaohuanji
			formItem.anquanzu = res.data.anquanzu
			formItem.wulijijiankong = res.data.wulijijiankong
			formItem.xunijijiankong = res.data.xunijijiankong
			formItem.gaojingguanli = res.data.gaojingguanli
			formItem.gaojingguize = res.data.gaojingguize
			formItem.yonghuguanli = res.data.yonghuguanli
			formItem.rizhi = res.data.rizhi
			formItem.shouquan = res.data.shouquan
			formItem.xiugaimima = res.data.xiugaimima
			formItem.anquanpeizhi = res.data.anquanpeizhi
			formItem.xitongguanli = res.data.xitongguanli
			console.log(formItem)
		})
};
permission()

/**
 * 建议：路由 path 路径与文件夹名称相同，找文件可浏览器地址找，方便定位文件位置
 *
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink: 链接地址不为空 2、isIframe:false`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isAffix：       是否固定在 tagsView 栏上
 *      isIframe：      是否内嵌窗口，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
 *      roles：         当前路由权限标识，取角色管理。控制路由显示、隐藏。超级管理员：admin 普通角色：common
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */

// 扩展 RouteMeta 接口
declare module 'vue-router' {
	interface RouteMeta {
		title?: string;
		isLink?: string;
		// isHide?: boolean;
		isKeepAlive?: boolean;
		isAffix?: boolean;
		isIframe?: boolean;
		roles?: string[];
		icon?: string;
	}
}

/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */

export const dynamicRoutes: Array<RouteRecordRaw> = [
	{
		path: '/',
		name: '/',
		component: () => import('/@/layout/index.vue'),
		redirect: '/Overview',
		meta: {
			iskeepalive: false,
		},
		children: [
			// 概览
			{
				path: '/Overview',
				name: 'Overview',
				component: () => import('/@/views/overview/index.vue'),
				meta: {
					title: 'message.router.Overview',
					isLink: '',
					isHide: !formItem.gailan,
					iskeepalive: false,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont icon-zidingyibuju',
				},
			},
			// 资源池
			{
				path: '/ResourcePool',
				name: 'ResourcePool',
				component: () => import('/@/views/resourcePool/index.vue'),
				meta: {
					title: 'message.router.ResourcePool',
					isLink: '',
					isHide: false,
					iskeepalive: false,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont icon-neiqianshujuchucun',
				},
			},
			// 存储池
			{
				path: '/StoragePool',
				name: 'StoragePool',
				component: () => import('/@/views/storagePool/index.vue'),
				meta: {
					title: 'message.router.StoragePool',
					isLink: '',
					isHide: false,
					iskeepalive: false,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont icon-zidingyibuju',
				},
			},
			// 网络管理
			{
				path: '/NetworkManage',
				name: 'NetworkManage',
				redirect: '/NetworkManage/DistributedSwitch',
				component: () => import('/@/layout/routerView/childs.vue'),
				meta: {
					title: 'message.router.NetworkManage',
					isLink: '',
					isHide: false,
					iskeepalive: false,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont icon-diqiu',
				},
				children: [
					{
						path: '/NetworkManage/DistributedSwitch', // 分布式交换机
						name: 'DistributedSwitch',
						component: () => import('/@/views/networkManage/distributedSwitch/index.vue'),
						meta: {
							title: 'message.router.DistributedSwitch',
							isLink: '',
							isHide: false,
							iskeepalive: true,
							isAffix: false,
						},
					},
					// {
					// 	path: '/NetworkManage/Switch', // 本地交换机
					// 	name: 'Switch',
					// 	component: () => import('/@/views/networkManage/switch/index.vue'),
					// 	meta: {
					// 		// title: 'message.router.Switch',
					// 		title: '集群交换机',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: true,
					// 		isAffix: false,
					// 	},
					// },
					// {
					// 	path: '/NetworkManage/Network', // 超融合网络
					// 	name: 'Network',
					// 	component: () => import('/@/views/networkManage/network/index.vue'),
					// 	meta: {
					// 		title: 'message.router.Network',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: true,
					// 		isAffix: false,
					// 	},
					// },
					// {

					{
						path: '/NetworkManage/Firewall', // 防火墙
						name: 'Firewall',
						component: () => import('/@/views/networkManage/firewall/index.vue'),
						meta: {
							title: 'message.router.Firewall',
							isLink: '',
							isHide: false,
							iskeepalive: true,
							isAffix: false,
						},
					},
					{
						path: '/NetworkManage/Securityroup', // 安全组
						name: 'Securityroup',
						component: () => import('/@/views/networkManage/securityroup/index.vue'),
						meta: {
							title: 'message.router.Securityroup',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
						},
					},
					// {
					// 	path: '/NetworkManage/Topology', // 拓扑
					// 	name: 'Topology',
					// 	component: () => import('/@/views/networkManage/topology/index.vue'),
					// 	meta: {
					// 		title: 'message.router.Topology',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: true,
					// 		isAffix: false,
					// 	},
					// },
					// {
					// 	path: '/NetworkManage/Test', // 测试
					// 	name: 'Test',
					// 	component: () => import('/@/views/networkManage/test/index.vue'),
					// 	meta: {
					// 		title: '测试',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: true,
					// 		isAffix: false,
					// 	},
					// },
				],
			},
			// 监控告警
			{
				path: '/Monitoring',
				name: 'Monitoring',
				redirect: '/Monitoring/Physics',
				component: () => import('/@/layout/routerView/childs.vue'),
				meta: {
					iskeepalive: false,
					title: 'message.router.Monitoring',
					isLink: '',
					isHide: false,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont-vtl icon-fuwufangwenguanli',
				},
				children: [
					{
						path: '/Monitoring/Physics', // 物理机监控
						name: 'Physics',
						component: () => import('/@/views/monitoring/Physics.vue'),
						meta: {
							title: 'message.router.Physics',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							isType: '监控中心',
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/Monitoring/Virtual', // 虚拟机监控
						name: 'Virtual',
						component: () => import('/@/views/monitoring/Virtual.vue'),
						meta: {
							title: 'message.router.Virtual',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					// {
					// 	path: '/Monitoring/Storage', // 存储监控
					// 	name: 'Storage',
					// 	component: () => import('/@/views/monitoring/Storage.vue'),
					// 	meta: {
					// 		title: 'message.router.Storage',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: false,
					// 		isAffix: false,
					// 		isIframe: false,
					// 		roles: ['admin', 'common'],
					// 	},
					// },
					{
						path: '/Monitoring/AllAlarms', // 全部告警
						name: 'AllAlarms',
						component: () => import('/@/views/monitoring/allAlarms/index.vue'),
						meta: {
							title: 'message.router.AllAlarms',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							isType: '告警中心',
							roles: ['admin', 'common'],
						},
					},
					// {
					// 	path: '/Monitoring/ClusterAlarm', // 集群告警
					// 	name: 'ClusterAlarm',
					// 	component: () => import('/@/views/monitoring/clusterAlarm/index.vue'),
					// 	meta: {
					// 		title: 'message.router.ClusterAlarm',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: false,
					// 		isAffix: false,
					// 		isIframe: false,
					// 		isType: '告警中心',
					// 		roles: ['admin', 'common'],
					// 	},
					// },
					// {
					// 	path: '/Monitoring/StorageAlarm', // 存储告警
					// 	name: 'StorageAlarm',
					// 	component: () => import('/@/views/monitoring/storageAlarm/index.vue'),
					// 	meta: {
					// 		title: 'message.router.StorageAlarm',
					// 		isLink: '',
					// 		isHide: false,
					// 		iskeepalive: false,
					// 		isAffix: false,
					// 		isIframe: false,
					// 		roles: ['admin', 'common'],
					// 	},
					// },
					{
						path: '/Monitoring/AlarmRules', // 告警规则
						name: 'AlarmRules',
						component: () => import('/@/views/monitoring/alarmRules/index.vue'),
						meta: {
							title: 'message.router.AlarmRules',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
				],
			},
			// 系统功能
			{
				path: '/System',
				name: 'System',
				redirect: '/System/User',
				component: () => import('/@/layout/routerView/childs.vue'),
				meta: {
					isKeepAlive: true,
					title: 'message.router.System',
					isLink: '',
					isHide: !formItem.xitonggongneng,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont-vtl icon-rizhiguanli',
				},
				children: [
					{
						path: '/System/User',
						name: 'User',
						component: () => import('/@/views/system/user/User.vue'),
						meta: {
							title: 'message.router.User',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/System/LogTabs',
						name: 'LogTabs',
						component: () => import('/@/views/system/logTabs/index.vue'),
						meta: {
							title: 'message.router.LogTabs',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/System/Authorize',
						name: 'Authorize',
						component: () => import('/@/views/system/Authorize.vue'),
						meta: {
							title: 'message.router.Authorize',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/System/Password',
						name: 'Password',
						component: () => import('/@/views/system/Password.vue'),
						meta: {
							title: 'message.router.Password',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/System/Secure',
						name: 'Secure',
						component: () => import('/@/views/system/secure/Secure.vue'),
						meta: {
							title: 'message.router.Secure',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/System/ManagementNode',
						name: 'ManagementNode',
						component: () => import('/@/views/system/managementNode/index.vue'),
						meta: {
							title: 'message.router.ManagementNode',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
				],
			},

			// 镜像管理 // 版本不使用
			{
				path: '/ImageManage',
				name: 'ImageManage',
				component: () => import('/@/views/imageManage/index.vue'),
				meta: {
					title: 'message.router.ImageManage',
					isLink: '',
					isHide: true,
					iskeepalive: false,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont icon-putong',
				},
			},
			// 测试菜单
			{
				path: '/TestMenu',
				name: 'TestMenu',
				redirect: '/TestMenu/DiskManage',
				component: () => import('/@/layout/routerView/childs.vue'),
				meta: {
					iskeepalive: false,
					title: 'message.router.TestMenu',
					isLink: '',
					isHide: true,
					isAffix: false,
					isIframe: false,
					roles: ['admin', 'common'],
					icon: 'iconfont icon-zidingyibuju',
				},
				children: [
					{
						path: '/TestMenu/DiskManage',
						name: 'DiskManage',
						component: () => import('/@/views/testMenu/DiskManage.vue'),
						meta: {
							title: 'message.router.DiskManage',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/TestMenu/LogicalStorage',
						name: 'LogicalStorage',
						component: () => import('/@/views/testMenu/LogicalStorage.vue'),
						meta: {
							title: 'message.router.LogicalStorage',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/TestMenu/Storages',
						name: 'Storages',
						component: () => import('/@/views/testMenu/Storages.vue'),
						meta: {
							title: 'message.router.Storages',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/TestMenu/Echarts',
						name: 'Echarts',
						component: () => import('/@/views/testMenu/Echarts.vue'),
						meta: {
							title: 'message.router.Echarts',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
					{
						path: '/TestMenu/DragTable',
						name: 'DragTable',
						component: () => import('/@/views/testMenu/DragTable.vue'),
						meta: {
							title: 'message.router.DragTable',
							isLink: '',
							isHide: false,
							iskeepalive: false,
							isAffix: false,
							isIframe: false,
							roles: ['admin', 'common'],
						},
					},
				],
			},

			// 存储管理 list

			// {
			// 	path: '/StoreList',
			// 	name: 'StoreList',
			// 	redirect: '/StoreList',
			// 	// component: () => import('/@/views/StoreList/index.vue'),
			// 	meta: {
			// 		isKeepAlive: true,
			// 		title: 'message.router.StoreList',
			// 		isLink: '',
			// 		isHide: false,
			// 		isAffix: false,
			// 		isIframe: false,
			// 		roles: ['admin', 'common'],
			// 		icon: 'iconfont icon-zidingyibuju',
			// 	},
			// 	children: [
			// 		{
			// 			path: '/StoreList',
			// 			name: 'StoreList',
			// 			component: () => import('/@/views/storeList/index.vue'),
			// 			meta: {
			// 				title: 'message.router.StoreList',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 		{
			// 			path: '/StoreList/Local',
			// 			name: 'Local',
			// 			component: () => import('/@/views/storeList/local/Local.vue'),
			// 			meta: {
			// 				title: 'message.router.Local',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 	],
			// },

			// 存储管理

			// {
			// 	path: '/StoreManage',
			// 	name: 'StoreManage',
			// 	redirect: '/StoreManage/IPsan',
			// 	component: () => import('/@/layout/routerView/childs.vue'),
			// 	meta: {
			// 		title: 'message.router.StoreManage',
			// 		isLink: '',
			// 		isHide: false,
			// 		isKeepAlive: true,
			// 		isAffix: false,
			// 		isIframe: false,
			// 		roles: ['admin', 'common'],
			// 		icon: 'iconfont icon-zidingyibuju',
			// 	},
			// 	children: [
			// 		{
			// 			path: '/StoreManage/StorageTable',
			// 			name: 'StorageTable',
			// 			component: () => import('/@/views/storeManage/storageTable/index.vue'),
			// 			meta: {
			// 				title: 'message.router.StorageTable',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 		{
			// 			path: '/StoreManage/IPsan',
			// 			name: 'IPsan',
			// 			component: () => import('/@/views/storeManage/ipSan/index.vue'),
			// 			meta: {
			// 				title: 'message.router.IPsan',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 		{
			// 			path: '/StoreManage/FCsan',
			// 			name: 'FCsan',
			// 			component: () => import('/@/views/storeManage/fcSan/index.vue'),
			// 			meta: {
			// 				title: 'message.router.FCsan',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 		{
			// 			path: '/StoreManage/NAS',
			// 			name: 'NAS',
			// 			component: () => import('/@/views/storeManage/nas/index.vue'),
			// 			meta: {
			// 				title: 'message.router.NAS',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 		{
			// 			path: '/StoreManage/NVME',
			// 			name: 'NVME',
			// 			component: () => import('/@/views/storeManage/nvme/index.vue'),
			// 			meta: {
			// 				title: 'message.router.NVME',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 		{
			// 			path: '/StoreManage/Distributed',
			// 			name: 'Distributed',
			// 			component: () => import('/@/views/storeManage/distributed/index.vue'),
			// 			meta: {
			// 				title: 'message.router.Distributed',
			// 				isLink: '',
			// 				isHide: false,
			// 				iskeepalive: false,
			// 				isAffix: false,
			// 				isIframe: false,
			// 				roles: ['admin', 'common'],
			// 			},
			// 		},
			// 	],
			// },
		],
	},
];

/**
 * 定义404、401界面
 * @link 参考：https://next.router.vuejs.org/zh/guide/essentials/history-mode.html#netlify
 */
export const notFoundAndNoPower = [
	{
		path: '/:path(.*)*',
		name: 'notFound',
		component: () => import('/@/views/error/404.vue'),
		meta: {
			title: 'message.staticRoutes.notFound',
			isHide: true,
		},
	},
	{
		path: '/401',
		name: 'noPower',
		component: () => import('/@/views/error/401.vue'),
		meta: {
			title: 'message.staticRoutes.noPower',
			isHide: true,
		},
	},
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
	{
		path: '/login',
		name: 'login',
		component: () => import('/@/views/login/index.vue'),
		meta: {
			title: '登录',
		},
	},
	/**
	 * 提示：写在这里的为全屏界面，不建议写在这里
	 * 请写在 `dynamicRoutes` 路由数组中
	 */
];
