<template>
	<el-dialog v-model="formItem.isShow" :title="formItem.titles" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item :label="formItem.label"
			>
        <el-input v-model="formItem.card" placeholder="请输入分布式交换机名称" v-show="false" />
        <span>已选 （ {{ formItem.card.length }} ）</span>
      </el-form-item>
			<div class="ztree-publick">
        <ZtreePublick :type="formItem.type" :zNodes="formItem.zNodes" @returnOK="returnOK"></ZtreePublick>
      </div>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { clusterAllocationQuery,clusterAllocation,hostAllocation,vmAllocationQuery,vmAllocation } from '/@/api/System'; // 接口

const ZtreePublick = defineAsyncComponent(() => import('/@/layout/component/ZtreePublick.vue'));
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	titles: '',
	label: '',
	userName: '',
	userID: '',
	node: '',
	card: [],
	type: '',
	zNodes: [{ id: '1', name: '资源节点', pid: '0' }],
});
const propCard = (rule: any, value: any, callback: any) => {
	if (!value || value.length === 0) {
		callback(new Error('请勾选下面虚拟机'));
	} else {
		callback();
	}
}; 
const rules = reactive<FormRules>({
	card: [{ validator: propCard, trigger: 'change' }],
});
const treeData = (node:string) => {
	// if(!true) {
	// 	formItem.zNodes = [
  //       // chkDisabled: true
	// 		{ id: '100', name: '集群1', pid: '' },
	// 		{ id: '1', name: '主机1', pid: '100' },
	// 		{ id: '4', name: '网卡1', pid: '1' },
	// 		{ id: '5', name: '网卡2', pid: '1' },
	// 	];
	// }else {
	
	// }
	if(formItem.node == 'cluster') {
		clusterAllocationQuery({user_id:formItem.userID})
		.then((res) => {
			formItem.zNodes = res.data;
		})
	}else if(formItem.node == 'host') {

	}else {
		vmAllocationQuery({user_id:formItem.userID})
		.then((res) => {
			formItem.zNodes = res.data;
		})
	}

};
const returnOK = (val: any,type: any) => {
	if(type == 'none') {
		formItem.card = []
	}else {
		if(formItem.node == 'cluster') {
			formItem.card = val.filter((node: any) => node.type == 'cluster');
		}else if(formItem.node == 'host') {
			formItem.card = val.filter((node: any) => node.type == 'host');
		}else {
			formItem.card = val.filter((node: any) => node.type == 'domain');
		}
	}
};
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				if(formItem.node == 'cluster') {
					clusterAllocation({
						nodes: formItem.card,
						user_id: formItem.userID,
						// user_name: formItem.userName,
					}).then((res) => {
						if(res.msg == 'ok') {
							ElMessage.success(formItem.titles+'操作完成');
							emit('returnOK', 'refresh');
						}else {
							ElMessage.error(res.msg);
						}
						});
				}else if	(formItem.node == 'host') {
					hostAllocation({
						nodes: formItem.card,
						user_id: formItem.userID,
						// user_name: formItem.userName,
					}).then((res) => {
						if(res.msg == 'ok') {
							ElMessage.success(formItem.titles+'操作完成');
							emit('returnOK', 'refresh');
						}else {
							ElMessage.error(res.msg);
						}
					})
				}else {
					vmAllocation({
						nodes: formItem.card,
						user_id: formItem.userID,
						// user_name: formItem.userName,
					}).then((res) => {
						if(res.msg == 'ok') {
							ElMessage.success(formItem.titles+'操作完成');
							emit('returnOK', 'refresh');
						}else {
							ElMessage.error(res.msg);
						}
					})
				}
			}
		});
	}
};
const openDialog = (row:any,node:string) => {
  formItem.isShow = true;
	formItem.node = node;
	formItem.userName = row.username;
	formItem.userID = row.id;
	if( node == 'cluster') {
		formItem.titles = '集群分配';
		formItem.label = '集群';
	}else if( node == 'host') {
		formItem.titles = '主机分配';
		formItem.label = '主机';
	}else {
		formItem.titles = '虚拟机分配';
		formItem.label = '虚拟机';
	}
  nextTick(() => {
		setTimeout(() => {
			formItem.type = '资源池分配' + new Date();
			treeData(node);
		}, 200);
  })
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.ztree-publick {
	height: 400px;
}
</style>