/**
 * views personal
 */

// 定义选项类型
interface SelectOptionType {
  value: string | number;
  label: string;
}

// 定义搜索类型
interface TableSearchType {
  label: string;
  prop: string;
  placeholder: string;
  required: boolean;
  type: string;
  options?: SelectOptionType[];
  isShow: boolean;
  isMultiple?: boolean;
  clearable?: boolean;
}

// 定义表格列类型
interface TableColumn {
  prop?: string;
  label?: string;
  width?: string | number;
  minWidth?: string | number;
  fixed?: boolean | 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  type?: string;
  slot?: string;
  headerSlot?: string;
  sortable?: boolean;
  showOverflowTooltip?: boolean;
}

// 定义分页参数类型
interface PaginationConfig {
  currentPage: number;
  pageSize: number;
  total: number;
  pageSizes?: number[];
}

// 定义分页变化事件参数类型
interface PageChangeParams {
  page: number;
  size: number;
}

// 导出类型定义
export {
  SelectOptionType,
  TableSearchType,
  TableColumn,
  PaginationConfig,
  PageChangeParams
};