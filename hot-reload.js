const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 清除缓存目录
const clearCache = () => {
  const cacheDirs = [
    path.resolve(process.cwd(), 'node_modules/.vite'),
    path.resolve(process.cwd(), 'node_modules/.cache')
  ];

  cacheDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`清除缓存目录: ${dir}`);
      fs.rmSync(dir, { recursive: true, force: true });
    }
  });
};

// 启动 Vite 开发服务器
const startVite = () => {
  console.log('启动 Vite 开发服务器...');
  
  // 设置环境变量
  const env = Object.assign({}, process.env, {
    VITE_PORT: 3000,
    VITE_OPEN: 'false',
    VITE_OPEN_CDN: 'false',
    VITE_PUBLIC_PATH: '/'
  });

  // 启动 Vite 开发服务器
  const vite = spawn('npx', ['vite', '--mode', 'dev'], {
    stdio: 'inherit',
    env
  });

  vite.on('error', (error) => {
    console.error('启动 Vite 开发服务器时出错:', error);
  });

  vite.on('exit', (code) => {
    console.log(`Vite 开发服务器已退出，退出代码: ${code}`);
  });
};

// 执行清除缓存和启动 Vite
clearCache();
startVite(); 