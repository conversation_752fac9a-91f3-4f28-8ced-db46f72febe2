<template>
    <div class="echarts" ref="chartRef"></div>
</template>

<script lang="ts">
import {defineComponent, onBeforeUnmount, onMounted, ref, watch} from 'vue';
import * as echarts from 'echarts';

export default defineComponent({
    name: 'ECharts',
    props: {
        options: {
            type: Object,
            default: () => ({}),
        },
    },
    setup(ctx) {
        let chart: any = null;


        const chartRef = ref();

        const initEcharts = () => {
            if (!chart) {
                chart = echarts.init(chartRef.value);
            }
            if (!ctx.options) return;
            chart.setOption(ctx.options);
        };
        const eventListener = () => {
            if (chart) {
                chart.resize();
            }
        };

        const resizeObserver = new ResizeObserver(eventListener);

        onMounted(() => {
            initEcharts();
            window.addEventListener('resize', eventListener);
            resizeObserver.observe(chartRef.value);
        });

        onBeforeUnmount(() => {
            window.removeEventListener('resize', eventListener);
            resizeObserver.disconnect();
        });
        watch(
            () => ctx.options,
            (val) => {
                initEcharts();
            },
            {
                deep: true,
            }
        );

        return {
            chartRef
        };
    },
});
</script>

<style lang="scss" scoped>
.echarts {
    width: 100%;
    height: 100%;
}
</style>