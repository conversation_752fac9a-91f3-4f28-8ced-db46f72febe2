const fs = require('fs');
const path = require('path');

// 递归查找所有 .vue 和 .ts 文件
function findFiles(dir, extensions = ['.vue', '.ts']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (file !== 'node_modules' && file !== 'dist') {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// 修复文件中的导入路径
function fixImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复 .ts 扩展名的导入
    const tsImportRegex = /from\s+['"]([^'"]+)\.ts['"]/g;
    if (tsImportRegex.test(content)) {
      content = content.replace(tsImportRegex, "from '$1'");
      modified = true;
    }
    
    // 修复 /index.ts 的导入
    const indexTsRegex = /from\s+['"]([^'"]+)\/index\.ts['"]/g;
    if (indexTsRegex.test(content)) {
      content = content.replace(indexTsRegex, "from '$1/index'");
      modified = true;
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed imports in: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  console.log('Finding files to fix...');
  
  const files = findFiles(srcDir);
  console.log(`Found ${files.length} files to check`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixImportsInFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`Fixed imports in ${fixedCount} files`);
}

main();
