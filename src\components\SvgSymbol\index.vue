<template>
  <svg 
    class="svg-symbol" 
    :class="className"
    :style="iconStyle" 
    aria-hidden="true"
  >
    <use :xlink:href="`#${iconName}`"></use>
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string           // 图标名称，如 'icon-gailan'
  size?: number | string // 图标大小
  color?: string         // 图标颜色，不传则自动继承父级颜色
  className?: string     // 自定义类名
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
})

const iconName = computed(() => props.name)

const iconStyle = computed(() => {
  const style: Record<string, any> = {
    width: typeof props.size === 'number' ? `${props.size}px` : props.size,
    height: typeof props.size === 'number' ? `${props.size}px` : props.size,
    fill: props.color || 'currentColor'
  }
  
  return style
})
</script>

<style>
/* 全局样式，强制覆盖所有 SVG symbol 的颜色 */
.svg-symbol {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  fill: currentColor !important;
}

.svg-symbol * {
  fill: inherit !important;
}
</style> 