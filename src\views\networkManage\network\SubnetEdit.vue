<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="修改子网"
    class="dialog-500"
  >
    <div class="form-switch-area">
      <el-form
        ref="ruleFormRef"
        :model="formItem"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="当前子网" prop="cidrid">
          <el-select v-model="formItem.cidrid" style="width: 100%" @change="subnetClick">
            <el-option v-for="item in formItem.cidrData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="子网名称" prop="name">
          <el-input v-model="formItem.name" placeholder="例：***********/24"/>
        </el-form-item>
        <el-form-item label="网关地址" prop="gateway">
          <el-input v-model="formItem.gateway" placeholder="请输入网关地址"/>
        </el-form-item>
        <!-- <el-form-item label="启用DHCP">
				  <el-checkbox v-model="formItem.dhcp" />
        </el-form-item> -->
        <div v-if="formItem.dhcp" style="display: flex;justify-content: space-between">
          <el-form-item label="起始IP" prop="start" >
            <el-input v-model="formItem.start" placeholder="例：***********"/>
          </el-form-item>
          <el-form-item label="结束IP" prop="end">
            <el-input v-model="formItem.end" placeholder="例：*************"/>
          </el-form-item>
        </div>
        <el-form-item label="DNS">
          <el-input v-model="formItem.dns" placeholder="请输入DNS"/>
        </el-form-item>
        <el-form-item label="主机路由">
          <div class="route-input">
            <el-input v-model="formItem.destination" placeholder="网段例：***********/24"/>
            <el-input v-model="formItem.nexthop" placeholder="吓一跳例：********"/>
            <el-button :icon="Plus" circle @click="routeAdd"/>
          </div>
        </el-form-item>
        <div class="route-datas" v-if="formItem.routeData.length>0">
          <div v-for="(item,index) in formItem.routeData" :key="index" class="route-item">
            <span class="route-wd">{{ item.destination }}</span>
            <span class="route-xyt">{{ item.nexthop }}</span>
            <el-button :icon="Delete" text type="danger" @click="routeDelete(index)"/>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { Plus,Delete } from '@element-plus/icons-vue'
import { netSubnetNew } from '/@/api/Network'; // 接口
import { propName,propVlan,propSub,ipInSub,subnetCheck,startEnd } from '/@/model/network.ts'; // 表列、正则


const props = defineProps({
  editTime: {
    type: String,
    required: true
  },
  tableRow: {
    type: Object,
    required: true
  }
});
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  cidrData: [{ label: '', value: 0 }],
  gatewayData: [],
  subnetData: [],
  allocationPools: [{start:'',end:''}],
  dnsNameservers: [],
  hostRoute: [[],[]],
  cidrname: '',
  cidrid: 0,
  name: '',
  subnet: '',
  subnetID: '',
  gateway: '',
  dhcp: false,
  start: '',
  end: '',
  dns: '',
  destination: '',
  nexthop: '',
  routeData: [{destination:'',nexthop:''}],
});
const subnetClick = (value:any)=>{
  const selectedOption = formItem.cidrData.find(option => option.value === value);
  if (selectedOption) {
    formItem.cidrname = selectedOption.label;
  }
  formItem.name = formItem.cidrname.split(":")[0];
  formItem.gateway = formItem.gatewayData[value]
  formItem.subnetID = formItem.subnetData[value]
  formItem.dns = formItem.dnsNameservers[value]
  formItem.start = formItem.allocationPools[value].start?formItem.allocationPools[value].start:''
  formItem.end = formItem.allocationPools[value].end?formItem.allocationPools[value].end:''
  formItem.routeData = formItem.hostRoute[value].length>0?formItem.hostRoute[value].slice():[]

}
// 添加主机路由
const routeAdd = ()=>{
  if(formItem.destination!==''&& formItem.nexthop!=='') {
    formItem.routeData.push({
      destination: formItem.destination,
      nexthop: formItem.nexthop
    })
    formItem.destination = '';
    formItem.nexthop = '';
  }else {
    ElMessage.warning('请输入正确的主机路由');
  }
}
// 删除主机路由
const routeDelete = (index: number)=>{
  formItem.routeData.splice(index,1);
}
const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        netSubnetNew({
          allocation_pools: [{start:formItem.start,end:formItem.end}],
          host_routes: formItem.routeData,
          gateway_ip: formItem.gateway,
          subnet_id: formItem.subnetID,
          dns_nameservers: formItem.dns!==''?formItem.dns.split(","):[],
          name: formItem.name,
        })
        .then(res => {
          if(res.msg == 'ok') {
            formItem.isShow = false;
            emit('returnOK', 'refresh');
            ElMessage.success('添加子网操作已完成');
          }else if(res.msg == '409'){
            ElMessage.error('网关地址不能出现在DHCP地址范围内');
          }else{
            ElMessage.error(res.msg);
          }
        })
      }
    })
  }
}

watch(
  ()=> props.editTime,
  (val)=>{
    formItem.isShow = true;
    formItem.cidrData = props.tableRow.cidr.map((item:string, index:number) => {
        return {
            label: item,
            value: index
        };
    });
    formItem.gatewayData = props.tableRow.gateway_ip // 网关
    formItem.subnetData = props.tableRow.subnets // subnetID
    formItem.allocationPools = props.tableRow.allocation_pools // IP池
    formItem.dnsNameservers = props.tableRow.dns_nameservers // DNS
    formItem.hostRoute = props.tableRow.host_routes // 路由池
    formItem.cidrname = formItem.cidrData[0].label
    formItem.cidrid = formItem.cidrData[0].value
    formItem.name = formItem.cidrname.split(":")[0]
    formItem.gateway = formItem.gatewayData[formItem.cidrid]
    formItem.subnetID = formItem.subnetData[formItem.cidrid]
    formItem.dns = formItem.dnsNameservers[formItem.cidrid]
    formItem.start = formItem.allocationPools[formItem.cidrid].start?formItem.allocationPools[formItem.cidrid].start:''
    formItem.end = formItem.allocationPools[formItem.cidrid].end?formItem.allocationPools[formItem.cidrid].end:''
    formItem.routeData = formItem.hostRoute[formItem.cidrid].length>0?formItem.hostRoute[formItem.cidrid].slice():[]

    if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
      ruleFormRef.value.resetFields();
    }
  }
);

const propGateway = (rule: any, value: any, callback: any) => {
  let regex = /^(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-4]|2[0-4][0-9]|[01]?[1-9][0-9]?|1[0-9]{2}|2[0-4][0-9])$/
  if (regex.test(value)) {
		ipInSub(value, formItem.subnet) ? callback() : callback(new Error("与子网不匹配"));
	} else {
		callback(new Error('无效网关地址'));
	}
}
const propStart = (rule: any, value: any, callback: any) => {
  if (subnetCheck(formItem.gateway,value)) {
		callback()
	} else {
		callback(new Error('请输入同一网段的不同IP'));
	}
}
const propEnd = (rule: any, value: any, callback: any) => {
  if (startEnd(formItem.start,value)) {
		callback()
	} else {
		callback(new Error('结束IP应大于起始IP'));
	}
}
const rules = reactive<FormRules>({
  cidrid: [{ required: true, message: '必选项', trigger: 'bulk' }],
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "bulk" }  
  ],
  gateway: [
    { required: true, message: '必填项' },
    { validator: propGateway, trigger: "blur" }
  ],
  start: [
    { required: true, message: '必填项' },
    { validator: propStart, trigger: "blur" }
  ],
  end: [
    { required: true, message: '必填项' },
    { validator: propEnd, trigger: "blur" }
  ],
})
</script>
<style lang="scss" scoped>
  .form-switch-area {
    width: 100%;
    overflow: auto;
    .route-input {
      width: 100%;
      display: flex;
    }
    .route-datas {
      height: 70px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .route-item {
        width: 49%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .route-wd {
          display: inline-block;
          width: 50%;
        }
        .route-xyt {
          display: inline-block;
          width: 42%;
        }
      }
    }
  }
</style>