<template>
	<div class="monitoring-area layout-padding">
    <el-card>
      <div class="log-general-area">
        <div class="tabs-btn-area">
          <el-button type="primary" plain @click="ignoreClick(state.tableSelect)">忽略</el-button>
          <el-button type="primary" plain @click="refresh">刷新</el-button>
        </div>
        <div class="tabs-table-area">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
						<!-- 类型 -->
						<template #severity="{ row }">
							<span :style="{color:typeConversion('color',row.severity)}">{{typeConversion('text',row.severity)}}</span>
						</template>
						<!-- 操作 -->
						<template #operation="{ row }">
            	<el-button type="primary" @click="ignoreClick([row])">忽略</el-button>
						</template>
          </my-table>
        </div>
      </div>
    </el-card>
		<Ignore :ignoreRow='state.ignoreRow' :ignoreTime='state.ignoreTime' ignoreType='存储' @returnOK="returnOK"></Ignore>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { alarmColumns,typeConversion } from '/@/model/logManage.ts'; // 表列、正则
import { storageAlarmQuery,storageAlarmIgnore } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const Ignore = defineAsyncComponent(() => import('../generalPublic/Ignore.vue'));

// 定义变量内容
const state = reactive({
  columns: alarmColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSelect: [],
  ignoreRow: [],
  ignoreTime: '',
  ignoreType: '',
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = []
	return new Promise(async(resolve)=>{
    storageAlarmQuery().then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 忽略
const ignoreClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    state.ignoreRow = arr
    state.ignoreTime = ''+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  refresh()
}
onMounted(() => {
})
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
  .log-general-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;

    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
    }
  }
}
.el-card {
  width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
    height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>