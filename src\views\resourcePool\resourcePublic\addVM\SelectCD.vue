<template>
  <el-dialog
    v-model="state.isShow"
    append-to-body
    title="选择磁盘"
    destroy-on-close
    :close-on-click-modal="false"
    class="dialog-1000"
  >
    <div class="disk-selection-container">
      <!-- 左侧存储池列表 -->
      <div class="storage-pool-panel">
        <div class="panel-header">
          <el-icon><FolderOpened /></el-icon>
          <span>存储池列表</span>
        </div>
        <div class="pool-items">
          <div
            v-for="item in state.poolData"
            :key="item.id"
            class="pool-item"
            :class="{ 'active': state.poolID === item.id }"
            @click="existingClick(item)"
          >
            <div class="pool-icon">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="pool-info">
              <div class="pool-name" :title="item.name">{{ item.name }}</div>
              <div class="pool-capacity">
                <span class="capacity-value">{{ byteUnitConversion(item.capacity) }} (总容量)</span>
                <span class="capacity-label">{{ item.type_code_display }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧磁盘表格 -->
      <div class="disk-table-panel">
        <div class="search-bar">
          <el-input
            v-model="state.tableSearch"
            placeholder="搜索磁盘"
            clearable
            prefix-icon="Search"
            @clear="refresh"
            @keyup.enter="refresh"
          >
            <template #append>
              <el-button :icon="Search" @click="refresh"></el-button>
            </template>
          </el-input>
        </div>

        <div class="table-container">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            row-key="id"
            highlight-current-row
          >
            <!-- 单选 -->
            <template #radio="{ row }">
              <el-radio-group v-model="state.tableID" @change="radioClick(row)">
                <el-radio :value="row.id"></el-radio>
              </el-radio-group>
            </template>
            <!-- 已用 -->
            <template #allocation="{ row }">
              <div class="capacity-info">
                <span class="capacity-value">{{ byteUnitConversion(row.allocation) }}</span>
              </div>
            </template>
            <!-- 总量 -->
            <template #capacity="{ row }">
              <span class="capacity-total">{{ byteUnitConversion(row.capacity) }}</span>
            </template>
          </my-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm" :disabled="!state.tableID">确认选择</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { storagePollQuery,DiskQuery } from '/@/api/ResourcePool/storage.ts'; // 接口
import { diskVMColumns,byteUnitConversion } from '/@/model/storage.ts';

import { Search, FolderOpened, DataBoard } from '@element-plus/icons-vue'
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

interface poolList {
  isShow: boolean;
  columns: Array<any>;
  pagination: {
    show: boolean;
  };
  poolData: Array<any>;
  poolID: string;
  tableID: string;
  poolName: string;
  tableName: string;
  tablePath: string;
  tableType: string;
  tableSize: string;
  tableSearch: string;
  treeID: string
}
const state = reactive<poolList>({
  isShow: false,
  columns: diskVMColumns,
  pagination: {
		show: true,
	}, // 是否显示分页
  poolData: [],
  poolID: '',
  poolName: '',
  tableID: '',
  tableName: '',
  tablePath: '',
  tableType: '',
  tableSize: '',
  tableSearch: '',
  treeID: '',
});
// 查询存储池
const storagePollData = () => {
  storagePollQuery({
    _id: state.treeID,
    type: 'host',
    page: 0,
    pagecount: 10,
    search_str: '',
    order_type: 'desc',
    order_by: '',
  }).then((res: any) => {
    state.poolData = res.data;
    state.poolID = res.data[0].id;
    state.poolName = res.data[0].name;
    refresh()
  })
}
// 点击存储池
const existingClick =(item:any)=>{
  state.poolID = item.id
  state.poolName = item.name
  refresh()
}
const getTableData = ( params: EmptyObjectType,page: EmptyObjectType) => {
  return new Promise(async(resolve)=>{
    DiskQuery({
      volume_type: 'iso',
      pool_id: state.poolID, // 存储池ID
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
        state.tableID = res.total*1>0?res.data[0].id:''
        state.tableName = res.total*1>0?res.data[0].name:''
        state.tablePath = res.total*1>0?res.data[0].path:''
        // state.tableFormat = res.total*1>0?res.data[0].format:''
        state.tableSize = res.total*1>0?byteUnitConversion(res.data[0].capacity):''
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {})

  })
}
// 刷新
const tableRef = ref();
const refresh = ()=>{
  // tableRef.value.handleSearch(); // 收索事件 表1页
  tableRef.value.refresh(); // 刷新事件 表当前
}
// 单选磁盘
const radioClick=(row:any)=>{
  state.tableID = row.id
  state.tableName = row.name
  state.tablePath = row.path
  state.tableType = row.type_code

  state.tableSize = byteUnitConversion(row.capacity)
}
const emit = defineEmits(['existingReturn']);
const confirm =()=>{
  state.isShow= false
  emit('existingReturn', {
    poolName:state.poolName,
    poolID:state.poolID,
    tableName:state.tableName,
    diskID:state.tableID,
    tablePath:state.tablePath,
    tableType:state.tableType,
  });
}
const existingDialog = (row: any) => {
  state.isShow = true;
  nextTick(() => {
    state.treeID = row.id
    storagePollData()
  })
};
// 暴露变量
defineExpose({
	existingDialog,
});
</script>
<style lang="scss" scoped>
.disk-selection-container {
  display: flex;
  height: 550px;
  border-bottom: 1px solid #ebeef5;
}

.storage-pool-panel {
  width: 220px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  background-color: #f9fafc;

  .panel-header {
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #f2f6fc;
  }

  .pool-items {
    flex: 1;
    overflow-y: auto;
    padding: 12px;

    .pool-item {
      display: flex;
      align-items: center;
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid #ebeef5;
      background-color: white;

      &:hover {
        background-color: #ecf5ff;
        transform: translateY(-2px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      }

      &.active {
        background-color: #409eff;
        color: white;
        border-color: #409eff;
        box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.3);

        .pool-name {
          color: white;
          font-weight: 600;
        }

        .capacity-value {
          color: white;
        }
        .pool-icon {
          background-color: #8c8c8c;
        }
        .capacity-label {
          color: #d5e3f7;
        }
      }

      .pool-icon {
        font-size: 24px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(64, 158, 255, 0.1);

        .el-icon {
          color: #ffb440;
        }
        .active & {
          background-color: rgba(255, 255, 255, 0.3);
          .el-icon {
            color: #ffffff;
          }
        }
      }

      .pool-info {
        flex: 1;
        min-width: 0;

        .pool-name {
          font-weight: 500;
          margin-bottom: 6px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .pool-capacity {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .capacity-value {
            font-size: 12px;
            font-weight: 600;
          }

          .capacity-label {
            font-size: 10px;
            // color: #000000;
          }
        }
      }
    }
  }
}

.disk-table-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 15px;
  
  .search-bar {
    margin-bottom: 16px;

    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px #dcdfe6 inset;
    }

    :deep(.el-input__inner) {
      height: 40px;
    }
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
}

.capacity-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .capacity-value {
    font-size: 12px;
    font-weight: 500;
    color: #303133;
  }

}

.capacity-total {
  font-weight: 500;
  color: #606266;
}

.dialog-footer {
  padding: 16px 20px;
  text-align: right;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;

  .el-table__header th {
    font-weight: 600;
    color: #606266;
  }

  .el-table__row {
    cursor: default;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }

    &.current-row {
      background-color: #ecf5ff;
    }

    .el-radio {
      cursor: pointer;
    }
  }
}

:deep(.el-radio) {
  margin-right: 0;

  .el-radio__inner {
    width: 18px;
    height: 18px;

    &::after {
      width: 8px;
      height: 8px;
    }
  }

  .el-radio__label {
    display: none;
  }
}

:deep(.el-table__row.current-row) {
  background-color: #ecf5ff;

  td {
    background-color: #ecf5ff !important;
  }
}
</style>