const selectableUsernames = new Set(['sysadm', 'adtadm', 'secadm']);
const userColumns = [
	{
		type: 'selection',
		selectable: (row: any) => !selectableUsernames.has(row.username),
		wrap: true,
	},
	{ label: '登录账号', prop: 'username' },
	{ label: '用户名称', prop: 'name', align: 'center' },
	{ label: '密码有效期', tdSlot: 'expiredday', align: 'center' },
	{ label: '角色', tdSlot: 'role_name', align: 'center' },
	{ label: '用户状态', tdSlot: 'status', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: 120, wrap: true },
];
const translationRole = (item: string) => {
	switch (item) {
		case 'sysadm':
			return '系统管理员';
			break;
		case 'adtadm':
			return '审计管理员';
			break;
		case 'secadm':
			return '安全管理员';
			break;
		case 'operator':
			return '操作员';
			break;
		default:
			return '未知';
	}
};
const tableDisabled = (item: string) => {
	switch (item) {
		case 'sysadm':
			return true;
		case 'adtadm':
			return true;
		case 'secadm':
			return true;
		case 'operator':
			return false;
		default:
			return false;
	}
};
// 管理节点
const managementNodeColumns = [
	{ type: 'selection', wrap: true },
	{ label: '节点名称', prop: 'name' },
	{ label: 'IP', prop: 'ip', align: 'center' },
	{ label: 'CPU', prop: 'cpu', align: 'center' },
	{ label: '内存', tdSlot: 'mem', align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: 120, wrap: true },
];

export { userColumns, translationRole, tableDisabled, managementNodeColumns };
