<template>
	<div>
		<el-dialog v-model="state.isShow" append-to-body class="dialog-1000">
			<template #header="{ close, titleId, titleClass }">
				<span class="el-dialog__title">{{ state.switchForm.name }} 交换机-下行</span>
			</template>
			<div class="dialog-area">
				<div class="tabs-btn-area">
					<div>
						<el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
					</div>
					<div v-if="powerItem.sousuo">
						<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
							<template #append>
								<el-button :icon="Search" @click="refresh"></el-button>
							</template>
						</el-input>
					</div>
				</div>
				<div class="table-area" v-if="powerItem.liebiao">
					<my-table ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData" @selectionChange="selectChange">
						<!-- 端口组 -->
						<template #port_group="{ row }">
							<span>{{ row.port.port_group.name }}</span>
						</template>
						<!-- 端口 -->
						<template #port="{ row }">
							<span>{{ row.port.name }}</span>
						</template>
					</my-table>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="state.isShow = false">关闭</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { descendingQuery  } from '/@/api/Network'; // 接口
import { descendingColumns } from '/@/model/network.ts'; // 表列、正则
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const state = reactive({
	isShow: false,
	columns: descendingColumns,
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	switchForm: {
		id:'',
		name:''
	},
	tableSelect: [],
});

const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
	return new Promise(async (resolve) => {
		descendingQuery({
			switch_id: state.switchForm.id, // 存储池ID
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		state.isShow = true;
		state.switchForm = row;
		if(tableRef.value){
			refresh();
		}
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'xiaxingliebiao',
    'xiaxingsousuo',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.xiaxingliebiao;
		powerItem.sousuo = res.data.xiaxingsousuo;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
.dialog-area {
	height: 650px;
	width: 100%;
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.table-area {
		position: relative;
		height: calc(100% - 50px);
		width: 100%;
	}
}
</style>