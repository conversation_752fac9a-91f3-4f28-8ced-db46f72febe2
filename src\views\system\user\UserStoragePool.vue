<template>
	<el-dialog v-model="formItem.isShow" title="存储池分配" append-to-body class="dialog-700">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="存储池">
        <el-input v-model="formItem.tableSelect" v-show="false" />
        <span>已选 （ {{ formItem.tableSelect.length }} ）</span>
      </el-form-item>
		</el-form>
		<div class="tabs-table-area">
      <my-table ref="tableRef" :pagination="formItem.pagination" :columns="formItem.columns" :request="getTableData" @selectionChange="selectChange">
        <!-- 总容量 -->
        <template #capacity="{ row }">
          <span>{{ capacityConversion(row.capacity) }}</span>
        </template>
        <!-- 已分配容量 -->
        <template #allocation="{ row }">
          <span>{{ capacityConversion(row.allocation) }}</span>
        </template>
        <!-- 可用容量 -->
        <template #available="{ row }">
          <span>{{ capacityConversion(row.available) }}</span>
        </template>
      </my-table>
    </div>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { storageAllocationQuery,storageAllocation } from '/@/api/System'; // 接口
import { capacityConversion } from '/@/model/resource.ts'; // 表格 正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));

const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	userName: '',
	userID: '',
	tableSearch: '',
	columns: [
		{ type: 'selection', wrap: true },
		{ label: '名称', prop: 'name', sortable: true, align: 'left' },
		{ label: '总容量', tdSlot: 'capacity', align: 'center' },
		{ label: '可用容量', tdSlot: 'available', align: 'center' },
	], // 表格表头配置
	pagination: {
		show: false,
	}, // 是否显示分页
	tableSelect: [],
	tableData: [],
});
// 表格选中变化
const selectChange = (row: any) => {
	formItem.tableSelect = row;
};
const propCard = (rule: any, value: any, callback: any) => {
	if (!value || value.length === 0) {
		callback(new Error('请勾选下面存储池'));
	} else {
		callback();
	}
}; 
const rules = reactive<FormRules>({
	tableSelect: [{ validator: propCard, trigger: 'change' }],
});
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	return new Promise(async (resolve) => {
		await storageAllocationQuery({
			user_id: formItem.userID,
		})
			.then((res: any) => {
				// 处理数据，根据需要设置 checked 属性
				const processedData = res.data.map((item: any) => ({
					...item,
					// 如果数据中已有 checked 属性，保持原值；否则根据业务逻辑设置
					checked: item.checked !== undefined ? item.checked : false
				}));

				resolve({
					data: processedData, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {});
	});
};
// 刷新
const tableRef = ref();
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				storageAllocation({
					nodes: formItem.tableSelect,
					user_id: formItem.userID,
				}).then((res:any) => {
          if(res.msg == 'ok') {
							ElMessage.success('存储池分配操作完成');
						}else {
							ElMessage.error(res.msg);
						}
        })
			}
		});
	}
};
const openDialog = (row:any) => {
  formItem.isShow = true;
  formItem.userName = row.username;
  formItem.userID = row.id;
  nextTick(() => {
    if(tableRef.value){
			refresh();
		}
  })
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.tabs-table-area {
  margin-top: 30px;
	width: calc(100%);
	height: 500px;
	position: relative;
}
</style>