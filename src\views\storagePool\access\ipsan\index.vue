<template>
  <div class="store-area">
      <div class="page-btn-area">
         <div class="tabs-btn-area">
          <div>
            <el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
            <el-button type="primary" plain @click="accessClick" v-if="powerItem.jieru">接入存储设备</el-button>
            <el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
          </div>
          <div v-if="powerItem.sousuo">
            <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
              <template #append>
                <el-button :icon="Search" @click="refresh"></el-button>
              </template>
            </el-input>
          </div>
        </div>
        <div class="tabs-table-area" v-if="powerItem.liebiao">
          <my-table
            ref="tableRef"
            :pagination="state.pagination"
            :columns="state.columns"
            :request="getTableData"
            @selectionChange='selectChange'
          >
            <!-- 名称详情 -->
						<template #device_name="{ row }">
              <!-- <el-link @click="detailClick(row)" :underline="false" type="success">{{ row.device_name }}</el-link> -->
              <el-link @click="detailClick(row)" :underline="false" type="success"><el-icon class="el-icon--left"><View /></el-icon> {{ row.device_name }}</el-link>
						</template>
            <!-- CHAP认证 -->
						<template #username="{ row }">
              <span>{{ row.username?'是':'否' }}</span>
						</template>
            <!-- 关联主机数 -->
						<template #hosts="{ row }">
              <el-tooltip effect="dark" :disabled="row.hosts==0">
                <template #content>
                  <div class="tooltip-table-area">
                    <el-table :data="row.hosts" >
                      <el-table-column prop="cluster_name" label="集群" />
                      <el-table-column prop="hostname" label="物理机" />
                      <el-table-column prop="ip" label="主机IP" />
                    </el-table>
                  </div>
                </template>
                <el-tag :type="row.hosts?.length>0?'success':'info'">{{ row.hosts?.length }}</el-tag>
              </el-tooltip>
						</template>
            <!-- 存储资源 -->
						<template #targets="{ row }">
              <el-tooltip effect="dark" :disabled="row.targets==0">
                <template #content>
                  <div class="tooltip-table-area">
                    <el-table :data="row.targets" >
                      <el-table-column prop="target_name" label="IQN名称" />
                    </el-table>
                  </div>
                </template>
                <el-tag :type="row.targets?.length>0?'success':'info'">{{ row.targets?.length }}</el-tag>
              </el-tooltip>
						</template>
            <!-- 操作 -->
						<template #operation="{ row }">
              <el-dropdown trigger="click" @command="commandItem($event,row)" v-if="powerItem.tianjia || powerItem.xiugai || powerItem.saomiao || powerItem.shanchu">
                <el-button type="primary" >操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon></el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="new" v-if="powerItem.tianjia">添加存储池</el-dropdown-item>
                    <el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item>
                    <el-dropdown-item command="sm" v-if="powerItem.saomiao">扫描</el-dropdown-item>
                    <el-dropdown-item command="sc" style="color:red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <span v-else>-</span>
						</template>
          </my-table>
        </div>
      </div>
    <Details ref="detailRef"></Details>
    <TableDocking ref="dockingRef" @returnOK="returnOK"></TableDocking>
    <TableNew ref="newRef" @returnOK="returnOK"></TableNew>
		<TableDelete :names='formDelet.tableNames' :deleteTime='state.deleteTime' @returnOK="returnOK"></TableDelete>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick,watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus';
import { ipTableQuery,ipTableDelete,ipTableScan } from '/@/api/StoreManage'; // 接口
import { iscsiColumns } from '/@/model/storeManage.ts'; // 表列、正则
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const Details = defineAsyncComponent(() => import('./details/index.vue'))
const TableDocking = defineAsyncComponent(() => import('./TableDocking.vue'))
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'))
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

// 定义变量内容
const state = reactive({
  columns: iscsiColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
	}, // 是否显示分页
  tableSearch: '',
  tableSelect: [],
  tableRow: {},
  deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
  if(!true){
		return {
			data: [
        {device_name:'list1',ip:'***********:3206',share:false,id:'aa1'},
        {device_name:'list2',ip:'**********:3206',share:true,id:'aa2'}
      ], // 数据
			total: 2, // 总数
		};
  }
	return new Promise(async(resolve)=>{
    ipTableQuery({
      page: page.pageNum, // 当前页
      pagecount: page.pageSize, // 每页条
      order_type: page.order, // 排序规则
      order_by: page.sort, // 排序列
      search_str: state.tableSearch, // 搜索条件
    }).then((res:any)=>{
      resolve({
        data: res.data, // 数据
        total: res.total*1 // 总数
      })
    }).catch((err:any) => {
      resolve({
        data: [], // 数据
        total: 0 // 总数
      })
    })
  })
};
// 刷新
const tableRef = ref();
const refresh = ()=>{
  tableRef.value.handleSearch(); // 收索事件 表1页
  // tableRef.value.refresh(); // 刷新事件 表当前
}
// 表格选中变化
const selectChange = (row: any)=>{
  state.tableSelect = row
}
// 详情
const detailRef = ref();
const detailClick = (row: any)=>{
  detailRef.value.openDialog(row,powerItem.saomiao)
}
// 接入存储
const dockingRef = ref();
const accessClick = ()=>{
  dockingRef.value.openDialog('new',{})
}
// 添加
const newRef = ref();
// 表操作列
const commandItem = (item: string,row:any)=>{
  state.tableRow = row
  switch (item) {
    case 'new':
      newRef.value.openDialog(row)
      break;
    case 'bj':
      dockingRef.value.openDialog('edit',row)
      break;
    case 'sm':
      ElMessageBox.confirm(`是否扫描存储资源： <span style="font-weight: 800">${row.device_name}</span>？`, {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          ipTableScan({id: row.id }).then((res) => {
            if (res.msg == 'ok') {
            // if (true) {
              ElMessage.success('存储资源扫描操作完成');
            } else {
              ElMessage.error('存储资源扫描操作失败');
            }
          });
        })
		    .catch(() => {});
      console.log('扫描');
      break;
    case 'sc':
      deleteClick([row])
      break;
  }
}
// 删除
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.device_name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = 'IPsan/'+new Date()
  }
}
// 返回数据
const returnOK = (item:any)=>{
  if(item == 'delete') {
    ipTableDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then(res => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除存储资源操作完成');
      }else {
		    ElMessage.error(res.msg);
      }
    })
  }else {
    refresh()
  }
}
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  jieru: false,
  tianjia: false,
  xiugai: false,
  saomiao: false,
  shanchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'IPsanliebiao',
    'IPsansousuo',
    'IPsanjieru',
    'IPsantianjiacunchuchi',
    'IPsanxiugai',
    'IPsansaomiao',
    'IPsanshanchu',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.IPsanliebiao;
		powerItem.sousuo = res.data.IPsansousuo;
		powerItem.jieru = res.data.IPsanjieru;
		powerItem.tianjia = res.data.IPsantianjiacunchuchi;
		powerItem.xiugai = res.data.IPsanxiugai;
		powerItem.saomiao = res.data.IPsansaomiao;
		powerItem.shanchu = res.data.IPsanshanchu;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style scoped lang="scss">
.store-area {
  padding-top: 15px;
	width: calc(100%);
	height: calc(100%);
  .page-btn-area {
    width: 100%;
    height: 100%;
    .tabs-btn-area {
      height: 50px;
      display: flex;
      justify-content: space-between;
    }
    .tabs-table-area {
      width: calc(100%);
      height: calc(100% - 50px);
      position: relative;
      
    }
  }
}
.tooltip-table-area {
  width: 450px;
}
</style>