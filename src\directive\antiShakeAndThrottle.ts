import type { App } from 'vue';

export function antiShakeAndThrottle(app: App) {
	// 定义全局自定义指令
	app.directive('antiShake', {
		// 在元素的 attribute 或事件监听器被应用之前调用
		created(el, binding) {
			let cbFun: NodeJS.Timeout | null = null;
			// const { time, event } = [];
			el.addEventListener(
				'click',
				(event: any) => {
					event && event.stopImmediatePropagation();
					if (!cbFun) {
						cbFun = setTimeout(() => {
							binding.value.event();
						}, 500);
					} else {
						clearTimeout(cbFun);
						cbFun = setTimeout(() => {
							binding.value.event();
						}, 500);
					}
				},
				true
			);
		},
	});

	//节流函数
	app.directive('throttle', {
		created(el, binding) {
			let throtTime = binding.value;
			if (!throtTime) {
				throtTime = 500;
			}
			let cbFun: NodeJS.Timeout | null = null;
			el.addEventListener(
				'click',
				(event: any) => {
					if (!cbFun) {
						cbFun = setTimeout(() => {
							cbFun = null;
						}, throtTime);
					} else {
						event && event.stopImmediatePropagation();
					}
				},
				true
			);
		},
	});
}
