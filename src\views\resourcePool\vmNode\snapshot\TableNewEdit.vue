<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    :title="formItem.type=='new'?'添加快照':'修改快照'"
    class="dialog-500"
  >
    <el-form
        ref="ruleFormRef"
        :model="formItem"
        :rules="rules"
        label-width="auto"
      >
        <el-form-item label="快照名称" prop="name">
          <el-input v-model="formItem.name"  placeholder="请输入快照名称"/>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="formItem.remark" :rows="2" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息"/>
        </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus';
import { vmSnapshotAdd,vmSnapshotEdit } from '/@/api/ResourcePool/vm.ts'; // 接口
import { propName } from '/@/model/resource.ts'; // 表列、正则
const ruleFormRef = ref<FormInstance>()
const formItem = reactive({
  isShow: false,
  type: '',
  name: '',
  vmName: '',
  vmID: '',
  remark: '',
});

const emit = defineEmits(['returnOK']);
const confirm =()=>{
  if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
    ruleFormRef.value.validate(val=>{
      if (val) {
        if (formItem.type == 'new') {
          vmSnapshotAdd({
            snapshot_name: formItem.name,
            remark: formItem.remark,
            vm_id: formItem.vmID,
            vm_name: formItem.vmName,
          })
          .then((res: any) => {
            if(res.msg == 'ok') {
              formItem.isShow = false;
              emit('returnOK', 'refresh');
              ElMessage.success('添加快照操作已完成');
            }else{
              ElMessage.error(res.msg);
            }
          })
        }else { 
          vmSnapshotEdit({
            snapshot_name: formItem.name,
            remark: formItem.remark,
            vm_id: formItem.vmID,
            vm_name: formItem.vmName,
          })
          .then((res:any) => {
            if(res.msg == 'ok') {
              formItem.isShow = false;
              emit('returnOK', 'refresh');
              ElMessage.success('修改快照操作已完成');
            }else{
              ElMessage.error(res.msg);
            }
          })
        }
      }
    })
  }
}
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '必填项' },
    { validator: propName, trigger: "blur" }  
  ],
})
// 打开弹窗
const openDialog = async (type:string,treeItem: any,row:any) => {
  formItem.isShow = true;
  formItem.vmName = treeItem.name
  formItem.vmID = treeItem.id
  formItem.type = type
	nextTick(() => {
    if (type == 'new') {
      if (ruleFormRef.value) { // 确保 ruleFormRef 已初始化
        ruleFormRef.value.resetFields();
      }
    }else {
      formItem.name = row.name
      formItem.remark = row.description
    }
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
  
</style>