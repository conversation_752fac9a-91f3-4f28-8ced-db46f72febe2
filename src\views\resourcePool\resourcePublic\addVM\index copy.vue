<template>
  <el-dialog
    v-model="formItem.isShow"
    append-to-body
    title="添加虚拟机"
    class="dialog-900"
  >
    <div class="vm-new-area">
      <div class="vm-step-area">
        <el-steps :active="formItem.current" simple>
          <el-step title="基本信息" :icon="Tickets" />
          <el-step title="硬件信息" :icon="Setting" />
          <el-step title="汇总信息" :icon="DocumentCopy" />
        </el-steps>
      </div>
      <div class="vm-new-content">
        <!-- 基本信息 -->
        <div class="vm-new-common">
          <!-- <AddBasic></AddBasic> -->
          <el-form
            ref="basicREF"
            label-position="left"
            :model="formBasic"
            :rules="rulesForm"
            label-width="150"
          >
            <el-form-item label="虚拟机名称" prop="name">
              <el-input v-model="formBasic.name"  placeholder="请输入虚拟机名称"/>
            </el-form-item>
            <el-form-item label="系统类型">
              <el-radio-group v-model="formBasic.systemType">
                <el-radio value="Linux">Linux</el-radio>
                <el-radio value="Windows">Windows</el-radio>
                <el-radio value="qita">其它</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="系统版本">
              <el-select v-model="formBasic.systemVersion" style="width:100%">
                <el-option
                  v-for="item in formItem.versionData"
                  :key="item.label"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="formBasic.notes" :rows="3" show-word-limit maxlength="50" type="textarea" placeholder="请输入备注信息"/>
            </el-form-item>
            <a class="vm-new-leve" @click="advancedClick">高级选项 
              <el-icon v-if="!formItem.advancedOptions"><ArrowRightBold/></el-icon>
              <el-icon v-if="formItem.advancedOptions"><ArrowDownBold /></el-icon>
            </a>
            <div v-show="formItem.advancedOptions">
              <el-form-item label="自动启动虚拟机">
                <el-switch
                  v-model="formBasic.selfStart"
                  inline-prompt
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
              <el-form-item label="是否加密">
                <el-switch
                  v-model="formBasic.encryption"
                  inline-prompt
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
              <el-form-item label="自动迁移">
                <el-switch
                  v-model="formBasic.migrate"
                  inline-prompt
                  active-text="开启"
                  inactive-text="关闭"
                />
              </el-form-item>
            </div>
            <el-form-item label=""></el-form-item>
          </el-form>
        </div>
        <!-- 硬件信息 -->
        <div class="vm-new-common">
          <el-form
            ref="hardwareREF"
            label-position="left"
            :rules="rulesForm"
            label-width="150"
          >
            <!-- CPU总数 -->
            <el-form-item>
              <template #label>
                <div class="vm-new-label" @click="formCPU.typeShow=!formCPU.typeShow">
                  <el-icon><ArrowDown v-show="formCPU.typeShow" /><ArrowRight v-show="!formCPU.typeShow"/></el-icon>
                  <span>CPU总数</span>
                </div>
              </template>
              <el-input v-model="formCPU.vcpus" :max="48" :min="2" type="number"/>
            </el-form-item>
            <div v-show="formCPU.typeShow">
              <el-form-item label="主CPU">
                <el-input v-model="formCPU.cores" :max="48" :min="2" type="number"/>
              </el-form-item>
              <el-form-item label="CPU最大值">
                <el-input v-model="formCPU.cpuMax" :max="48" :min="2" type="number"/>
              </el-form-item>
              <el-form-item label="CPU架构">
                <el-select v-model="formCPU.framework" style="width:100%">
                  <el-option label="64 位" value="64" />
                  <el-option label="32 位" value="32" />
                </el-select>
              </el-form-item>
              <el-form-item label="CPU工作模式">
                <el-select v-model="formCPU.mode" style="width:100%">
                  <el-option label="兼容" value="jr" />
                  <el-option label="主机匹配" value="zj" />
                  <el-option label="直通" value="zt" />
                </el-select>
              </el-form-item>
              <el-form-item label="CPU预留(%)">
                <el-input v-model="formCPU.reserve" :max="48" :min="2" type="number"/>
              </el-form-item>
              <el-form-item label="CPU预留(%)">
                <el-input v-model="formCPU.limit" :max="48" :min="2" type="number"/>
              </el-form-item>
            </div>
            <!-- 内存 -->
            <el-form-item>
              <template #label>
                <div class="vm-new-label" @click="formMEM.typeShow=!formMEM.typeShow">
                  <el-icon><ArrowDown v-show="formMEM.typeShow" /><ArrowRight v-show="!formMEM.typeShow"/></el-icon>
                  <span>内存</span>
                </div>
              </template>
              <el-input v-model="formMEM.memory" :min="1" type="number">
                <template #append>GB</template>
              </el-input>
            </el-form-item>
            <div v-show="formMEM.typeShow">
              <el-form-item label="主机剩余内存">
                <el-input v-model="formMEM.surplusMem" :min="1" type="number" disabled>
                  <template #append>GB</template>
                </el-input>
              </el-form-item>
              <el-form-item label="内存预留百分比">
                <el-input v-model="formMEM.reserveMem" :min="1" type="number" disabled>
                  <template #append>%</template>
                </el-input>
              </el-form-item>
              <el-form-item label="内存分配策略">
                <el-select v-model="formMEM.strategy" style="width:100%">
                  <el-option label="请选择类型" value="none" />
                  <el-option label="Strict" value="Strict" />
                  <el-option label="Preferred" value="Preferred" />
                  <el-option label="Interleave" value="Interleave" />
                </el-select>
              </el-form-item>
              <el-form-item label="绑定NUMA NODE">
                <el-input v-model="formMEM.bind" :min="1" disabled>
                  <template #append>
                    <el-button :icon="Search" />
                  </template>
                </el-input>
              </el-form-item>
            </div>
            <!-- 磁盘 -->
            <div v-for="(item, index) in diskData" ::key="'disk'+index">
              <el-form-item>
                <template #label>
                  <div class="vm-new-label" @click="item.typeShow=!item.typeShow">
                    <el-icon><ArrowDown v-show="item.typeShow" /><ArrowRight v-show="!item.typeShow"/></el-icon>
                    <span>{{ index==0?'磁盘':'磁盘'+index }}</span>
                  </div>
                </template>
                <el-input v-model="item.memory" :min="1" type="number" :disabled="item.source=='existing'">
                  <template #append>
                    <el-select v-model="item.unit" style="width: 100px">
                      <el-option label="KB" value="KB" />
                      <el-option label="MB" value="MB" />
                      <el-option label="GB" value="GB" />
                      <el-option label="TB" value="TB" />
                    </el-select>
                    <el-button v-if="item.remove" @click="removeDisk(index)" :icon="Delete" style="margin-left: 20px"/>
                  </template>
                </el-input>
              </el-form-item>
              <div v-show="item.typeShow">
                <el-form-item label="总线类型">
                  <el-select v-model="item.bus" style="width:100%">
                    <el-option label="高速硬盘" value="high" />
                    <el-option label="IDE硬盘" value="ide" />
                    <el-option label="SCSI硬盘" value="scsi" />
                    <el-option label="SATA硬盘" value="sata" />
                    <el-option label="USB硬盘" value="usb" />
                  </el-select>
                </el-form-item>
                <el-form-item label="缓存方式">
                  <el-select v-model="item.cache" style="width:100%">
                    <el-option label="directsync" value="directsync" />
                    <el-option label="writethrough" value="writethrough" />
                    <el-option label="writeback" value="writeback" />
                    <el-option label="none" value="none" />
                  </el-select>
                </el-form-item>
                <el-form-item label="系统类型">
                  <el-radio-group v-model="item.source">
                    <el-radio value="newbuilt" border>添加磁盘</el-radio>
                    <el-radio value="existing" border>现有磁盘</el-radio>
                  </el-radio-group>
                </el-form-item>
                <!-- 添加磁盘 -->
                <div v-if="item.source=='newbuilt'">
                  <el-form-item label="目标存储池">
                    <el-input v-model="item.newDiskName" />
                  </el-form-item>
                  <el-form-item label="磁盘名称">
                    <el-input v-model="item.poolName" disabled>
                      <template #append>
                        <el-button @click="selectStoragePool(index)" :icon="Search"/>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="存储格式">
                    <el-select v-model="item.format" style="width:100%">
                      <el-option label="qcow2" value="qcow2" />
                      <el-option label="gs" value="gs" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="置备类型">
                    <el-select v-model="item.preparation" style="width:100%">
                      <el-option label="精简置备" value="jj" />
                      <el-option label="厚置备延迟置零" value="yc" />
                      <el-option label="厚置备置零" value="zl" />
                    </el-select>
                  </el-form-item>
                </div>
                <!-- 现有磁盘 -->
                <div v-if="item.source=='existing'">
                  <el-form-item label="磁盘名称">
                    <el-input v-model="item.existingDiskName" disabled placeholder="选择现有磁盘">
                      <template #append>
                        <el-button @click="selectDisk(index)" :icon="Search"/>
                      </template>
                    </el-input>
                  </el-form-item>
                </div>
                <el-form-item></el-form-item>
                <el-form-item label="开启共享">
                  <el-switch v-model="item.share" inline-prompt
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </el-form-item>
                <el-form-item label="IO悬挂超时时长">
                  <el-input v-model="item.timeout" :min="1" type="number">
                    <template #append>秒</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input v-model="item.distNotes" :rows="3" show-word-limit type="textarea" placeholder="请输入备注信息"/>
                </el-form-item>
              </div>
            </div>
            <!-- 网卡 -->
            <div  v-for="(item, index) in netData" :key="'net'+index">
              <el-form-item>
                <template #label>
                  <div class="vm-new-label" @click="item.typeShow=!item.typeShow">
                    <el-icon><ArrowDown v-show="item.typeShow" /><ArrowRight v-show="!item.typeShow"/></el-icon>
                    <span>{{ index==0?'网卡':'网卡'+index }}</span>
                  </div>
                </template>
                <el-input v-model="item.net" disabled>
                  <template #append>
                    <el-button @click="selectNet(index)" :icon="Search"/>
                    <el-button v-if="item.remove" @click="removeNet(index)" :icon="Delete" style="margin-left: 20px"/>
                  </template>
                </el-input>
              </el-form-item>
              <div v-show="item.typeShow">
                <el-form-item label="网卡类型">
                  <el-select v-model="item.netType" style="width:100%">
                    <el-option label="高速网卡" value="high" />
                    <el-option label="普通网卡" value="ordinary" />
                    <el-option label="Intel e1000网卡" value="e1000" />
                    <el-option label="SE-IOV网卡" value="iov" />
                  </el-select>
                </el-form-item>
                <el-form-item label="内核加速" v-if="item.netType=='high'">
                  <el-switch v-model="item.accelerate" inline-prompt
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </el-form-item>
                <el-form-item label="VF网卡" v-if="item.netType=='iov'">
                  <el-select v-model="item.netVF" style="width:100%">
                    <el-option label="未知" value="high" />
                  </el-select>
                </el-form-item>
                <el-form-item label="MAC地址">
                  <el-input v-model="item.mac" />
                </el-form-item>
                <el-form-item label="网卡多队列" v-if="item.netType=='high'">
                  <el-switch v-model="item.queue" inline-prompt
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </el-form-item>
              </div>
            </div>
            <!-- 光驱 -->
            <div  v-for="(item, index) in driveData" :key="'drive'+index">
              <el-form-item>
                <template #label>
                  <div class="vm-new-label">
                    <span>{{ index==0?'光驱':'光驱'+index }}</span>
                  </div>
                </template>
                <el-input v-model="item.drive" disabled>
                  <template #append>
                    <el-button :icon="Search"/>
                    <el-button v-if="item.remove" @click="removeDrive(index)" :icon="Delete" style="margin-left: 20px"/>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="网卡多队列" v-show="item.drive!==''">
                <el-input v-model="item.drivestorage" disabled/>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <!-- 汇总信息 -->
        <div class="vm-new-common">
          bbbbbbbb
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-dropdown placement="top" trigger="click" @command="commandItem" v-if="formItem.current==1" style="float:left;">
          <el-button type="primary" >
          添加硬件<el-icon class="el-icon--right"><ArrowUpBold /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="disk">磁盘</el-dropdown-item>
              <el-dropdown-item command="net">网卡</el-dropdown-item>
              <el-dropdown-item command="drive">光驱</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button v-if="formItem.current>0" type="primary" @click="formItem.current--">上一步</el-button>
        <el-button @click="formItem.isShow = false">取消</el-button>
        <el-button v-if="formItem.current<2" type="primary" @click="formItem.current++">下一步</el-button>
        <el-button v-if="formItem.current==2" type="primary" @click="confirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { Search,Delete } from '@element-plus/icons-vue'
import { Tickets, Setting, DocumentCopy } from '@element-plus/icons-vue'
import { treeAddHost } from '/@/api/ResourcePool'; // 接口
import { linuxData, windowsData, qitaData } from '/@/model/vm.ts'; // 表列、正则

const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  addVmTime: {
    type: String,
    required: true
  }
});
const basicREF = ref<FormInstance>()
const hardwareREF = ref<FormInstance>()

const formItem = reactive({
  isShow: false,
  versionData: [],
  advancedOptions: false,
  current: 0,
});
// 基本信息
const formBasic = reactive({
  name: '',
  systemType: 'Linux',
  systemVersion: '',
  notes: '',
  selfStart: true,
  encryption: false,
  migrate: true,
});
// 硬件信息-CPU
const formCPU = reactive({
  typeShow: false,
  vcpus: 2,
  cores: 48,
  cpuMax: 2,
  framework: '64',
  mode: 'jr',
  reserve: 2,
  limit: 2,
})
// 硬件信息-内存
const formMEM = reactive({
  typeShow: false,
  memory: 2,
  surplusMem: 2,
  reserveMem: 1,
  strategy: 'none',
  bind: '',
})
// 硬件信息-磁盘
const diskData = reactive([
  {
    typeShow: false,
    remove: false,
    disk: 80,
    unit: 'GB',
    bus: 'high',
    cache: 'none',
    source: 'newbuilt',
    poolName: '',
    poolID: '',
    newDiskName: '',
    format: 'qcow2',
    preparation: 'jj',
    existingDiskName: '',
    existingDiskID: '',
    share: false,
    timeout: 120,
    distNotes: '',
  }
])
// 硬件信息-网卡
const netData = reactive([
  {
    typeShow: false,
    remove: false,
    net: '',
    netID: '',
    netType: 'high',
    accelerate: true,
    mac: '',
    queue: false,
    netVF: '',
  }
])
// 硬件信息-光驱
const driveData = reactive([
  {
    remove: false,
    drive: '',
    drivestorage: '',
  }
])

const propName = (rule:any, value:any, callback:any) => {
  const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/
  if (!value) {
    callback(new Error("必填项"))
  } else if (!regex.test(value)) {
    callback(new Error("2-32 个中文、英文、数字、特殊字符@_.-"))
  } else {
    callback()
  }
}
const propIP = (rule:any, value:any, callback:any) => {
  const regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  if (!value) {
    callback(new Error("必填项"))
  } else if (!regex.test(value)) {
    callback(new Error("请输入有效的IP地址"))
  } else {
    callback()
  }
}

const rulesForm = reactive<FormRules>({
  name: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propName, trigger: "blur" },
  ],
  ip: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propIP, trigger: "blur" },
  ]
})
// 添加硬件
const commandItem = (item: string)=>{
  if(item == 'disk') {
    diskData.push({
      typeShow: false,
      remove: true,
      disk: 80,
      unit: 'GB',
      bus: 'high',
      cache: 'none',
      source: 'newbuilt',
      poolName: '',
      poolID: '',
      newDiskName: '',
      format: 'qcow2',
      preparation: 'jj',
      existingDiskName: '',
      existingDiskID: '',
      share: false,
      timeout: 120,
      distNotes: '',
    })
  }else if(item == 'net'){
    netData.push({
      typeShow: false,
      remove: true,
      net: '',
      netID: '',
      netType: 'high',
      accelerate: true,
      mac: '',
      queue: false,
      netVF: '',
    })
  }else if(item == 'drive'){
    driveData.push({
      remove: true,
      drive: '',
      drivestorage: '',
    })
  }
}
// 删除磁盘
const removeDisk = (item: any)=>{
  diskData.splice(item,1)
}
// 目标存储池
const selectStoragePool = (item: any)=>{
  console.log('目标存储池')
}
// 磁盘选择
const selectDisk = (item: any)=>{
  console.log('磁盘选择')
}
// 删除网卡
const removeNet = (item: any)=>{
  netData.splice(item,1)
}
// 选择网卡
const selectNet = (item: any)=>{
  console.log('选择网卡')
}
// 删除光驱
const removeDrive = (item: any)=>{
  driveData.splice(item,1)
}

// 高级选项点击
const advancedClick = ()=>{
  formItem.advancedOptions = !formItem.advancedOptions
}

const emit = defineEmits(['returnOK']);

const confirm =()=>{
  if (basicREF.value) { // 确保 basicREF 已初始化
    basicREF.value.validate(val=>{
      if (val) {
        formItem.isShow = false;
        treeAddHost({
          // name: formItem.name
        })
        .then(res => {
          emit('returnOK', 'refresh');
          console.log('res',res)
        })
      }
    })
  }
}
watch(
  ()=> props.addVmTime,
  (val)=>{
    formItem.isShow = true;
    if (basicREF.value) { // 确保 basicREF 已初始化
      basicREF.value.resetFields();
    }
  }
);
watch(
  ()=> formBasic.systemType,
  (val)=>{
    if(val==='Windows') {
      formItem.versionData = linuxData
      formBasic.systemVersion = linuxData[0].label
    }else if(val==='Linux') {
      formItem.versionData = windowsData
      formBasic.systemVersion = windowsData[0].label
    }else if(val==='qita') {
      formItem.versionData = qitaData
      formBasic.systemVersion = qitaData[0].label
    }
  }
);
watch(
  ()=> formItem.current,
  (val)=>{
    let carousel = document.querySelector(".vm-new-content");
    carousel.style.transform = `translateX(-${val * 760}px)`;
  }
)
</script>
<style lang="scss" scoped>
  .vm-new-area {
    width: calc(100%);
    // height: 600px;
    .vm-step-area {
      margin-bottom: 10px;
    }
    .vm-new-content {
      width: 2300px;
      height: 500px;
      transform: translateX(0px);
      transition: transform 300ms;
      .vm-new-common {
        float: left;
        width: 760px;
        height: 500px;
        padding: 10px 50px;
        overflow: auto;
        .vm-new-leve {
          display: block;
          font-size: 20px;
          margin-bottom: 20px;
          cursor: pointer;
        }
      }
    }
  }
  .vm-new-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    >span{
      padding-left: 10px;
    }
  }
</style>