<template>
	<div>
		<el-dialog v-model="state.isShow" append-to-body class="dialog-1000">
			<template #header="{ close, titleId, titleClass }">
				<span class="el-dialog__title">{{ state.switchForm.name }} 交换机-上行</span>
			</template>
			<div class="dialog-area">
				<div class="tabs-btn-area">
					<div>
						<el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
						<el-button type="primary" plain @click="connectClick" v-if="powerItem.tianjia">绑定网卡</el-button>
					</div>
					<div>
						<!-- <el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
							<template #append>
								<el-button :icon="Search" @click="refresh"></el-button>
							</template>
						</el-input> -->
					</div>
				</div>
				<div class="table-area">
					<my-table ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData" @selectionChange="selectChange">
						<!-- 操作 -->
						<template #operation="{ row }">
							<el-button type="danger" plain @click="liftClick(row)" v-if="powerItem.shanchu">解除</el-button>
							<span v-else>-</span>
						</template>
					</my-table>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="state.isShow = false">关闭</el-button>
				</div>
			</template>
		</el-dialog>
		<CartConnect ref="connectRef" @returnOK="returnOK" />
	</div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { ascendingQuery, nicUnbind } from '/@/api/Network'; // 接口
import { ascendingColumns } from '/@/model/network.ts'; // 表列、正则
import { Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const CartConnect = defineAsyncComponent(() => import('./CartConnect.vue'));

const state = reactive({
	isShow: false,
	columns: ascendingColumns,
	pagination: {
		show: false,
	}, // 是否显示分页
	tableSearch: '',
	switchForm: {
		id: '',
		name: '',
	},
	tableSelect: [],
});

const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	state.tableSelect = [];
	return new Promise(async (resolve) => {
		ascendingQuery({
			switch_id: state.switchForm.id, // 存储池ID
			// page: page.pageNum, // 当前页
			// pagecount: page.pageSize, // 每页条
			// order_type: page.order, // 排序规则
			// order_by: page.sort, // 排序列
			// search_str: state.tableSearch, // 搜索条件
		})
			.then((res: any) => {
				resolve({
					data: res.data, // 数据
					total: res.total * 1, // 总数
				});
			})
			.catch((err: any) => {
				resolve({
					data: [], // 数据
					total: 0, // 总数
				});
			});
	});
};
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 绑定网卡
const connectRef = ref();
const connectClick = () => {
	connectRef.value.openCart(state.switchForm);
};
// 解除网卡
const liftClick = (row: any) => {
	ElMessageBox.confirm(`是否对当前网卡 ${row.name} 进行 解绑 操作?`, '解绑网卡', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			nicUnbind({
				switch_id: state.switchForm.id,
				nic_name: row.name,
				host_id: row.host_id,
			}).then((res: any) => {
				if (res.msg == 'ok') {
					ElMessage.success('解绑网卡操作完成');
					refresh();
				} else {
					ElMessage.error('解绑网卡操作失败');
				}
			});
		})
		.catch(() => {});
};
// 返回
const returnOK = (item: string) => {
	console.log('item', item);
	// refresh();
};
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		state.isShow = true;
		state.switchForm = row;
		if (tableRef.value) {
			refresh();
		}
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
  tianjia: false,
  shanchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'wangluojiaohuanjishangxingliebiao',
    'wangluojiaohuanjibangdingwangka',
    'wangluojiaohuanjijiebangwangka',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.wangluojiaohuanjishangxingliebiao;
		powerItem.tianjia = res.data.wangluojiaohuanjibangdingwangka;
		powerItem.shanchu = res.data.wangluojiaohuanjijiebangwangka;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
.dialog-area {
	height: 650px;
	width: 100%;
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.table-area {
		position: relative;
		height: calc(100% - 50px);
		width: 100%;
	}
}
</style>