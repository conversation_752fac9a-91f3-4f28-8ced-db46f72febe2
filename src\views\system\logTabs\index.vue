<template>
	<div class="monitoring-area layout-padding">
		<el-card>
			<div class="log-general-area">
				<TableSearch ref="searchRef" :searchParams="state.searchParams" :search="state.searchColumns" @search="onSearch" />
				<div class="btn-group">
					<el-button :icon="Search" type="primary" @click="onSearch">查询</el-button>
					<a :href="state.fileUrl" style="margin-left: 10px">
            <el-button :icon="Download" type="primary" :disabled='state.disabled'>导出日志</el-button>
          </a>
				</div>
			</div>
			<div class="log-table">
				<MyTable ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData">
					<!-- 角色 -->
					<template #role="{ row }">
						<span>{{ translationRole(row.role) }}</span>
					</template>
					<!-- 结果 -->
					<template #loglevel="{ row }">
						<el-tag :type="row.loglevel == '成功'?'success':'info'">{{ row.loglevel }}</el-tag>
					</template>
				</MyTable>
			</div>
		</el-card>
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { Search, Download } from '@element-plus/icons-vue';
import { dayjs } from 'element-plus';
import { translationRole } from '/@/model/system.ts';
import { logColumns,logSearch,defaultTime,cookieList } from '/@/model/logManage.ts'; // 表列、正则
import { logTableQuery, logCategoryQuery,logTypeQuery } from '/@/api/LogManage'; // 接口
import { ElMessage } from 'element-plus';
const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableSearch = defineAsyncComponent(() => import('/@/components/table/Search.vue'));
const searchRef = ref();

// 定义变量内容
const state = reactive({
	searchParams: {} as EmptyObjectType, // 搜索参数
	searchColumns: [] as Array<TableSearchType>, // 搜索表单配置
	columns: logColumns as Array<MyTableColumns>, // 表格表头配置
	pagination: {
		show: true,
		// btn: true,
	}, // 是否显示分页
	categoryData: [],
	actionData: [],
	fileUrl: 'javascript:void(0)',
	disabled: true,
	tableTotal: 0,
});
// 初始化搜索条件
const searchData = () => {
	let listData = logSearch(true)
	listData.forEach((em:any) => {
		if (em.prop == 'times') {
			// em.options = [defaultTime(0),defaultTime(24)]
			em.options = []
		}
		if (em.prop == 'category') {
			em.options = state.categoryData
		}
		if (em.prop == 'role') {
			em.options = cookieList(document.cookie)
		}
		if (em.prop == 'action') {
			em.options = state.actionData
		}
		if (em.prop == 'result') {
			em.options = [{ label: '成功', value: '成功' },{ label: '失败', value: '失败' }]
		}
	});
	state.searchColumns = listData
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
	console.log('查询参数',page);
	return new Promise(async (resolve) => {
		logTableQuery({
			// datetimeTo: state.searchParams.times==undefined?'':state.searchParams.times[1].replace(/-/g,"/"),
			datetimeFrom: state.searchParams.times==undefined?'':state.searchParams.times[0], // 开始时间
			datetimeTo: state.searchParams.times==undefined?'':state.searchParams.times[1], // 结束时间
			searchKey: [
          state.searchParams.username, 
          state.searchParams.category, 
          state.searchParams.action
      ].filter(item => item !== undefined&&item !== '').join(','),

			// user: state.searchParams.username, // 用户名
			// servername: state.searchParams.category==undefined?'':state.searchParams.category, // 类别
			system: state.searchParams.role==undefined?'':state.searchParams.role, // 角色
			// clientip: state.searchParams.action==undefined?'':state.searchParams.action, // 操作
			loglevel: state.searchParams.result==undefined?'':state.searchParams.result,  // 结果
			
			storeName: 'logdata', // 固定
			// currentId: state.tableTotal>0?state.tableTotal-((page.pageNum-1)*page.pageSize-1):0, // 当前页数据的索引
			pagesize: page.pageSize, // 每页显示条数
			pagenum: page.pageNum, // 当前页
			// currentStoreName: 'logdata', // 不需要
			// forward: true, // 不需要
			// ...(state.currentId == 0?{}:{currentId: state.currentId}), // 可能会用到
		}).then((res: any) => {
			state.tableTotal = res.result.count;
			resolve({
				data: res.result.data, // 数据
				total: res.result.count*1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
// 收索
const onSearch = () => {
	// 搜索事件
	state.searchParams = Object.assign({}, searchRef.value.form);
	state.tableTotal = 0;
	nextTick(() => {
		refreshTable();
	});
};

const tableRef = ref();
// 表格刷新事件
const refreshTable = () => {
	tableRef.value.handleSearch();
};
// 日志条件查询
const conditionQuery = ()=>{
	logCategoryQuery({
		full: 'true'
	})
	.then((res: any) => {
		state.categoryData=res.result.map((item:any) => {
			return {label: item,value: item}
		})
	})
	logTypeQuery({
		full: 'true'
	})
	.then((res: any) => {
		state.actionData=res.result.map((item:any) => {
			return {label: item,value: item}
		})
	})
	setTimeout(() => {
		searchData();
	}, 500);
}
onMounted(() => {
	conditionQuery()
});
</script>
<style scoped lang="scss">
.monitoring-area {
	padding-top: 0 !important;
	width: 100%;
	height: 100%;
	.log-general-area {
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: space-between;
		.btn-group {
			display: flex;
			justify-content: right;
		}
	}
	.log-table {
		width: 100%;
		height: calc(100% - 50px);
		position: relative;
	}
}
.el-card {
	width: 100%;
	height: 100%;
	--el-card-padding: 15px;
	:deep(.el-card__body) {
		height: 100%;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		.toolip-box {
			display: flex;
			justify-content: space-between;
			.btn-group {
				display: flex;
				justify-content: right;
			}
		}
		.table-content {
			height: calc(100%);
			padding-top: 10px;
			position: relative;
			.el-table {
				flex: 1;
				.status-info {
					color: var(--el-color-info);
				}
				.status-warn {
					color: var(--el-color-warning);
				}
				.status-error {
					color: var(--el-color-error);
				}
			}
		}
	}
}
</style>