<template>
	<el-form size="large" class="login-content-form">
		<el-form-item label="用户名">
			<el-input text :placeholder="$t('message.account.accountPlaceholder1')" v-model="state.ruleForm.userName" clearable autocomplete="off">
			</el-input>
		</el-form-item>
		<el-form-item label="密码">
			<el-input
				:type="state.isShowPassword ? 'text' : 'password'"
				:placeholder="$t('message.account.accountPlaceholder2')"
				v-model="state.ruleForm.passWord"
				autocomplete="off"
				@keyup.enter="onSignIn"
			>
			</el-input>
		</el-form-item>
		<el-button type="primary" class="login-content-submit" v-waves @click="onSignIn" :loading="state.loading.signIn">
			<span>{{ $t('message.account.accountBtnText') }}</span>
		</el-button>
	</el-form>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { logon } from '/@/api/login/index'; // 接口
import { md5 } from 'js-md5';
import { formItem, hasAnyPermission } from '/@/router/route';

// 定义变量内容
const { t } = useI18n();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
const state = reactive({
	isShowPassword: false,
	ruleForm: {
		userName: '',
		passWord: '',
	},
	loading: {
		signIn: false,
	},
	rememberUser: false,
});
// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});
// 登录
const onSignIn = async () => {
	state.loading.signIn = true;
	Session.set('token', '123'); // 前台路由配置roles,避免运行异常
	Session.set('userInfo', {
		roles: ['admin'], // 目前没有权限来控制菜单，默认以管理员方式
		userName: state.ruleForm.userName, // 登录后显示的名称
	});

	if (!themeConfig.value.isRequestRoutes) {
		// 先调用登录接口
		logon({
			username: state.ruleForm.userName,
			password: state.ruleForm.passWord, // 密码采用md5加密
		}).then(async (res) => {
			console.log('aaaaaa');
			if (res.msg === 'ok') {
				// 然后初始化前端路由控制
				const isNoPower = await initFrontEndControlRoutes();
				// 最后跳转页面
				signInSuccess(isNoPower);
			} else {
				ElMessage.error(res.msg);
				state.loading.signIn = false;
			}
		}).catch((error) => {
			ElMessage.error('登录失败');
			state.loading.signIn = false;
		});
	} else {
		// 如果是后端控制路由，先初始化路由再跳转
		const isNoPower = await initFrontEndControlRoutes();
		signInSuccess(isNoPower);
	}
};
// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {

	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = currentTime.value;
		// 登录成功，跳到转首页
		// if (false) {
		// 	router.push({
		// 		path: <string>route.query?.redirect,
		// 		query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
		// 	});
		// } else {
			// 检查是否有任何可用权限
			const hasPermission = hasAnyPermission();
			if (!hasPermission) {
				ElMessage.warning(`该账号无可用权限`);
				return;
			}
			// 按照指定顺序进行权限判定和跳转
			if (formItem.gailan) {
				console.log('gailan')
				router.push('Overview');
			}else if (formItem.ziyuanchi) {
				router.push('ResourcePool');
			} else if (formItem.cunchuguanli) {
				router.push('FileManagement');
			} else if (formItem.wangluoguanli) {
				if (formItem.fenbushijiaohuanji) {
					router.push('NetworkManagement/DistributedSwitch');
				}else if (formItem.anquanzu) {
					router.push('NetworkManagement/Securityroup');
				} else {
					router.push('/401');
				}
			} else if (formItem.jiankonggaojing) {
				if (formItem.wulijijiankong) {
					router.push('Monitoring/Physics');
				}else if(formItem.xunijijiankong) {
					router.push('Monitoring/Virtual');
				}else if(formItem.gaojingguanli) {
					router.push('Monitoring/AllAlarms');
				}else	if(formItem.gaojingguize) {
					router.push('Monitoring/AlarmRules');
				}else {
					router.push('/401');
				}
			} else if (formItem.xitonggongneng) {
				if(formItem.yonghuguanli) {
					router.push('System/User');
				}else if(formItem.rizhi) {
					router.push('System/LogTabs');
				}else if(formItem.shouquan) {
					router.push('System/Authorize');
				}else if(formItem.xiugaimima) {
					router.push('System/Password');
				}else if(formItem.anquanpeizhi) {
					router.push('System/Secure');
				}else if(formItem.xitongguanli) {
					router.push('System/SystemManagement');
				}else {
					router.push('/401');
				}
			} else {
				ElMessage.error('路由跳转失败');
			}
		// }
		// 登录成功提示
		const signInText = t('message.signInText');
		ElMessage.success(`${currentTimeInfo}，${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}
	state.loading.signIn = false;
};
</script>

<style scoped lang="scss">
.login-content-form {
	// margin-top: 20px;
	padding: 0 54px;
	position: relative;
	// height: calc(100% - 176px);
	height: 380px;
	:deep(.is-focus) {
		box-shadow: 0 0 4px 1px var(--el-input-focus-border-color);
	}
	:deep(.el-input__icon) {
		color: var(--el-input-focus-border-color);
	}
	:deep(.el-form-item) {
		display: block;
		margin-bottom: 30px;
		.el-form-item__label {
			font-weight: bold;
			color: #3d414f;
		}
		.el-input__wrapper {
			border-radius: 27px;
			.el-input__inner {
				text-indent: 15px;
			}
		}
	}
	:deep(.el-input--large) {
		height: 56px;
		--el-input-height: 56px;
	}
	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;
		&:hover {
			color: #909399;
		}
	}
	.login-content-code {
		width: 100%;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}
	.login-content-submit {
		width: 100%;
		letter-spacing: -2px;
		font-weight: bold;
		font-size: 14px;
		// position: absolute;
		// bottom: 80px;
		margin-top: 20px;
		border-radius: 27px;
		// left: 50%;
		height: 56px;
		// transform: translateX(-50%);
	}
}
</style>
