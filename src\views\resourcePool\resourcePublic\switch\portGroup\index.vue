<template>
	<div>
		<el-dialog v-model="state.isShow" append-to-body class="dialog-1000">
			<template #header="{ close, titleId, titleClass }">
				<span class="el-dialog__title">{{ state.switchForm.name }} 交换机-端口组</span>
			</template>
			<div class="dialog-area">
				<div class="tabs-btn-area">
					<div>
						<el-button type="primary" plain @click="refresh" v-if="powerItem.liebiao">刷新</el-button>
						<el-button type="primary" plain @click="newClick" v-if="powerItem.tianjia">添加端口组</el-button>
						<el-button type="danger" plain @click="deleteClick(state.tableSelect)" v-if="powerItem.shanchu">删除</el-button>
					</div>
					<div v-if="powerItem.sousuo">
						<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
							<template #append>
								<el-button :icon="Search" @click="refresh"></el-button>
							</template>
						</el-input>
					</div>
				</div>
				<div class="table-area">
					<my-table ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData" @selectionChange="selectChange">
						<!-- 实际使用量 -->
						<template #allocation="{ row }">
							<span>{{ row.allocation }}</span>
						</template>
						<!-- 操作 -->
						<template #operation="{ row }">
							<el-dropdown trigger="click" @command="commandItem($event, row)" v-if="powerItem.xiugai || powerItem.shanchu">
								<el-button type="primary"
									>操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon
								></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="bj" v-if="powerItem.xiugai">修改</el-dropdown-item>
										<el-dropdown-item command="sc" style="color: red" divided v-if="powerItem.shanchu">删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
							<span v-else>-</span>
						</template>
					</my-table>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="state.isShow = false">关闭</el-button>
					<!-- <el-button type="primary" @click="confirm">确认</el-button> -->
				</div>
			</template>
		</el-dialog>
    <TableNew ref="newRef" @returnOK="returnOK" />
    <TableEdit ref="editRef" @returnOK="returnOK" />
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { portGroupQuery,portGroupDelete  } from '/@/api/Network'; // 接口
import { portGroupColumns } from '/@/model/network.ts'; // 表列、正则
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'));
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const state = reactive({
	isShow: false,
	columns: portGroupColumns,
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	switchForm: {
		id:'',
		name:''
	},
	tableSelect: [],
	tableRow: {},
	deleteTime: '',
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
	return new Promise(async (resolve) => {
		portGroupQuery({
			id: state.switchForm.id, // 存储池ID
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// const emit = defineEmits(['portReturn']);
const confirm = () => {
	state.isShow = false;
	// emit('portReturn', 'cg');
};

const newRef = ref(); //  添加
const editRef = ref(); //  修改
// 添加交换机
const newClick = () => {
	newRef.value.openDialog(state.switchForm);
};
// 表操作列
const commandItem = (item: string, row: never) => {
	state.tableRow = row;
	switch (item) {
		case 'bj':
			editRef.value.openDialog(row);
			break;
		case 'sc':
      deleteClick([row])
			break;
	}
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 删除端口组
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '端口组/'+new Date()
  }
}
// 返回数据
const returnOK = (item: any) => {
	if(item == 'delete') {
    portGroupDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then((res:any) => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除端口组操作完成');
      }else {
        ElMessage.error('删除端口组操作失败');
      }
    })
  }else {
    refresh()
  }
};
// 打开弹窗
const openDialog = async (row: any) => {
	nextTick(() => {
		state.isShow = true;
		state.switchForm = row;
		if(tableRef.value){
			refresh();
		}
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
import { powerCodeQuery } from '/@/api/System'; // 权限
// 定义变量内容
const powerItem = reactive({
	liebiao: false,
	sousuo: false,
  tianjia: false,
  xiugai: false,
  shanchu: false,
});
const powerQuery = (() => {
	powerCodeQuery({module_code:[
    'duankouzuliebiao',
    'duankouzusousuo',
    'tianjiaduankouzu',
    'xiugaiduankouzu',
    'shanchuduankouzu',
  ]}).then((res:any)=>{
		powerItem.liebiao = res.data.duankouzuliebiao;
		powerItem.sousuo = res.data.duankouzusousuo;
		powerItem.tianjia = res.data.tianjiaduankouzu;
		powerItem.xiugai = res.data.xiugaiduankouzu;
		powerItem.shanchu = res.data.shanchuduankouzu;
	});
});
// 页面加载时
onMounted(() => {
	powerQuery()
});
</script>
<style lang="scss" scoped>
.dialog-area {
	height: 650px;
	width: 100%;
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.table-area {
		position: relative;
		height: calc(100% - 50px);
		width: 100%;
	}
}
</style>