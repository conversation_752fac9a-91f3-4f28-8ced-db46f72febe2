<template>
	<el-dialog v-model="formItem.isShow" title="修改用户" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="登录账号">
				<el-input v-model="formItem.username" disabled/>
			</el-form-item>
			<el-form-item label="用户名称">
				<el-input v-model="formItem.name" placeholder="请输入用户名"/>
			</el-form-item>
			<!-- <el-form-item label="用户角色" prop="role">
				<el-select v-model="formItem.role" style="width: 100%">
          <el-option label="审计员" value="adtadm" />
          <el-option label="安全员" value="secadm" />
          <el-option label="操作员" value="operator" />
        </el-select>
			</el-form-item> -->
			<el-form-item label="有效期" prop="time">
				<el-date-picker v-model="formItem.time" type="date" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup name="UserEdit">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { userEdit } from '/@/api/System'; // 接口
import { propName,timeFormat } from '/@/model/resource.ts'; // 表列、正则
const props = defineProps({
	editTime: {
		type: String,
		required: true,
	},
  tableRow: {
    type: Object,
    required: true,
  }
});
const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	username: '',
	name: '',
	role: '',
	time: '',
});

const rules = reactive<FormRules>({
	name: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'bulk' },
	],
	role: [{ required: true, message: '必选项', trigger: 'bulk' }],
	time: [{ required: true, message: '必选项', trigger: 'bulk' }]
});
const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				userEdit({
					username: formItem.username,
          name: formItem.name,
					// role: formItem.role,
					expiredday: timeFormat(formItem.time),
				}).then((res) => {
					if(res.msg == 'ok') {
        		ElMessage.success('修改用户操作完成');
						emit('returnOK', 'refresh');
					}else {
        		ElMessage.error(res.msg);
					}
				});
			}
		});
	}
};
watch(
	() => props.editTime,
	(val) => {
		formItem.isShow = true;
		formItem.username = props.tableRow.username
		formItem.name = props.tableRow.name
		formItem.role = props.tableRow.role
		formItem.time = props.tableRow.expiredday
	}
);
</script>