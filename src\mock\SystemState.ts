import { dayjs } from 'element-plus';
import { getData } from './index';

export const getDefaultInfo = () => {
	const data = {
		software: 'THE VTL',
		version: 'V2.0',
		serial_number: 'GEN00000000000923801037',
		cpu_info: [
			{
				cpu_module: 'HUAWEI,Kunpeng 920',
				cpu_cores: 64,
			},
		],
		fan_status: '正常',
		service_status: '',
		run_time: '1小时25分钟15秒',
	};
	return getData(data);
};

export const getPowerList = () => {
	const data = [
		{
			location: 'chassis',
			name: 'PAC900S12-B2',
			manufacturer: 'HUAWEI',
			serial_number: '2102312XWK10PB129452',
			model: '02312XWK',
			revision: 'DC',
			max_power: '900 W',
			status: 'Present, Critical',
			type: 'Switching',
			input_voltage: 'Auto-switch',
			plugged: 'No',
			hot_replace: 'Yes',
		},
		{
			location: 'chassis',
			name: 'PAC900S12-B2',
			manufacturer: 'HUAWEI',
			serial_number: '2102312XWK10PB129429',
			model: '02312XWK',
			revision: 'DC',
			max_power: '900 W',
			status: 'Present, OK',
			type: 'Switching',
			input_voltage: 'Auto-switch',
			plugged: 'Yes',
			hot_replace: 'Yes',
		},
	];
	return getData(data);
};

export const getFanList = () => {
	const data = [
		{
			name: 'fan1',
			status: 'ok',
			fan_speed: '7500.000',
		},
		{
			name: 'fan2',
			status: 'ok',
			fan_speed: '7500.000',
		},
		{
			name: 'fan3',
			status: 'ok',
			fan_speed: '7500.000',
		},
		{
			name: 'fan4',
			status: 'ok',
			fan_speed: '7500.000',
		},
	];
	return getData(data);
};

export const getPoolList = () => {
	const data = [
		{
			Name: 'vpool',
			Size: '159995816550400',
			Capacity: '8',
			Allocated: '13353495592960',
			Free: '146642320957440',
			Health: 'ONLINE',
			Freeing: '0',
		},
	];
	return getData(data);
};

export const getCpuList = () => {
	const data = [
		{
			Name: 'CPU1 Core Rem',
			Value: '46.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '105.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 Core Rem',
			Value: '43.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '105.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 MEM Temp',
			Value: '40.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '95.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 MEM Temp',
			Value: '39.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '95.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU Power',
			Value: '74.000',
			Unit: 'Watts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 VDDQ_AB',
			Value: '1.200',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '1.080',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '1.320',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 VDDQ_CD',
			Value: '1.200',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '1.080',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '1.320',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 VDDQ_AB',
			Value: '1.200',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '1.080',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '1.320',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 VDDQ_CD',
			Value: '1.200',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '1.080',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '1.320',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 VDDQ Temp',
			Value: '41.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '120.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 VDDQ Temp',
			Value: '41.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '120.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 VRD Temp',
			Value: '45.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '120.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 VRD Temp',
			Value: '44.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '120.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 VDDAVS',
			Value: '0.880',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '0.690',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '1.150',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 VDDAVS',
			Value: '0.850',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '0.690',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '1.150',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 VDDFIX',
			Value: '0.800',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '0.720',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '0.880',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 VDDFIX',
			Value: '0.800',
			Unit: 'Volts',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: '0.720',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: '0.880',
			Thresholds6: 'na',
		},
		{
			Name: 'PCIe2 Cpu Temp',
			Value: '41.000',
			Unit: 'degrees C',
			State: 'ok',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: '105.000',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 Prochot',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x0080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 Prochot',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x0080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 Status',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x8080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 Status',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x8080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU1 Memory',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x0080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU2 Memory',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x0080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
		{
			Name: 'CPU Usage',
			Value: '0x0',
			Unit: 'discrete',
			State: '0x0080',
			Thresholds1: 'na',
			Thresholds2: 'na',
			Thresholds3: 'na',
			Thresholds4: 'na',
			Thresholds5: 'na',
			Thresholds6: 'na',
		},
	];
	return getData(data);
};

export const getMonitorList = () => {
	const data = [
		{
			id: 10,
			timestamp: 1714011378,
			disk_r_bytes: 9,
			disk_w_bytes: 11,
			disk_r_ops: 26,
			disk_w_ops: 31,
			cpu_usage_rate: 45,
			mem_usage_rate: 1,
			net_rx_bytes: 14,
			net_wx_bytes: 26,
			fc_rx_bytes: 34,
			fc_wx_bytes: 50,
			dedupe_rate: 5,
		},
		{
			id: 11,
			timestamp: 1714011413,
			disk_r_bytes: 6,
			disk_w_bytes: 18,
			disk_r_ops: 30,
			disk_w_ops: 36,
			cpu_usage_rate: 40,
			mem_usage_rate: 9,
			net_rx_bytes: 15,
			net_wx_bytes: 29,
			fc_rx_bytes: 32,
			fc_wx_bytes: 46,
			dedupe_rate: 1,
		},
		{
			id: 12,
			timestamp: 1714011421,
			disk_r_bytes: 9,
			disk_w_bytes: 10,
			disk_r_ops: 30,
			disk_w_ops: 36,
			cpu_usage_rate: 42,
			mem_usage_rate: 10,
			net_rx_bytes: 13,
			net_wx_bytes: 27,
			fc_rx_bytes: 39,
			fc_wx_bytes: 46,
			dedupe_rate: 6,
		},
		{
			id: 13,
			timestamp: 1714011481,
			disk_r_bytes: 10,
			disk_w_bytes: 19,
			disk_r_ops: 30,
			disk_w_ops: 39,
			cpu_usage_rate: 40,
			mem_usage_rate: 7,
			net_rx_bytes: 10,
			net_wx_bytes: 24,
			fc_rx_bytes: 39,
			fc_wx_bytes: 46,
			dedupe_rate: 4,
		},
		{
			id: 14,
			timestamp: 1714011541,
			disk_r_bytes: 6,
			disk_w_bytes: 13,
			disk_r_ops: 24,
			disk_w_ops: 38,
			cpu_usage_rate: 41,
			mem_usage_rate: 6,
			net_rx_bytes: 11,
			net_wx_bytes: 29,
			fc_rx_bytes: 36,
			fc_wx_bytes: 41,
			dedupe_rate: 5,
		},
	];
	return getData(data);
};
