// 主机池
const tabPoolColumns = [
	{ label: '主机池名称', prop: 'name', align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 集群
const tabColonyColumns = [
	{ label: '集群名称', prop: 'name', align: 'center' },
	{ label: '主机', prop: 'hostname', align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 主机
const tabHostColumns = [
	{ label: '主机池', prop: 'pool_id', align: 'center' },
	{ label: '集群', prop: 'cluster_id', align: 'center' },
	{ label: '主机', prop: 'hostname', align: 'center' },
	{ label: '主机IP', prop: 'ip', align: 'center' },
	// { label: 'CPU架构', prop: 'cpu_architecture', align: 'center' },
	{ label: 'CPU数', prop: 'cpu', align: 'center' },
	{ label: '内存', prop: 'mem', align: 'center' },
	{ label: '维护模式', prop: 'host_model', align: 'center' },
	{ label: '高可用', prop: 'is_ha', align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 虚拟机
const vmColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name', sortable: true, align: 'left' },
	{ label: 'IP地址', prop: 'ip', align: 'center' },
	{ label: 'VCPU', prop: 'vcpu', align: 'center' },
	{ label: '内存', prop: 'memory', align: 'center' },
	{ label: '状态', tdSlot: 'status', align: 'center' },
	{ label: '高可用', prop: 'is_ha', align: 'center' },
	// { label: '集群', prop: 'pool', align: 'center' },
	// { label: '物理机', prop: 'pool', align: 'center' },
	// { label: '系统版本', prop: 'pool', align: 'center' },
	// { label: '状态', prop: 'pool', align: 'center' },
	// { label: 'IP地址', prop: 'ip', align: 'center' },
	// { label: 'CPU', prop: 'pool', align: 'center' },
	// { label: '内存', prop: 'pool', align: 'center' },
	// { label: '高可用', prop: 'remark', align: 'center' },
	// { label: '创建时间', prop: 'remark', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];
// 存储池
const storageColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name', sortable: true, align: 'left' },
	{ label: '类型', prop: 'type_code_display', align: 'center' },
	{ label: '总容量', tdSlot: 'capacity', align: 'center' },
	{ label: '已分配容量', tdSlot: 'allocation', align: 'center' },
	{ label: '可用容量', tdSlot: 'available', align: 'center' },
	{ label: '存储目录', prop: 'storage_local_dir', align: 'center' },
	{ label: '磁盘', tdSlot: 'storage_volume', align: 'center' },
	{ label: '是否启用', prop: 'status', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
];

// 虚拟机回收站
const vmRecycleColumns = [
	{ type: 'selection', wrap: true },
	{ label: '名称', prop: 'name', sortable: true, align: 'left' },
	{ label: 'IP地址', prop: 'ip', align: 'center' },
	{ label: 'VCPU', prop: 'vcpu', align: 'center' },
	{ label: '内存', prop: 'memory', align: 'center' },
	{ label: '状态', prop: 'status', align: 'center' },
	{ label: '高可用', prop: 'is_ha', align: 'center' },
	// { label: '集群', prop: 'pool', align: 'center' },
	// { label: '物理机', prop: 'pool', align: 'center' },
	// { label: '系统版本', prop: 'pool', align: 'center' },
	// { label: '状态', prop: 'pool', align: 'center' },
	// { label: 'IP地址', prop: 'ip', align: 'center' },
	// { label: 'CPU', prop: 'pool', align: 'center' },
	// { label: '内存', prop: 'pool', align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 180, align: 'center', wrap: true },
];
const netColumns = [
	{ label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '虚拟交换机', prop: 'name', align: 'center' },
	// { label: '端口组', prop: 'port_name', sortable: true, align: 'center' },
	{ label: '网卡名称', prop: 'pnics', align: 'center' },
	{ label: 'VLAN ID', prop: 'vlan_ids', align: 'center' },
	{ label: '类型', tdSlot: 'vswitch_type', align: 'center' },
];
// 备份
const backupsColumns = [
	// { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '备份', prop: 'name', sortable: true, align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 快照
const snapshotColumns = [
	{ type: 'selection', wrap: true },
	{ label: '快照名称', prop: 'name', sortable: true },
	{ label: '虚拟机', tdSlot: 'vm_name', sortable: true, align: 'center' },
	{ label: '创建时间', prop: 'creation_time', align: 'center' },
	{ label: '描述', prop: 'description', sortable: true, align: 'center' },
	{ label: '操作', tdSlot: 'operation', width: 120, align: 'center', wrap: true },
]
// 运行
const runColumns = [
	// { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '运行日志', prop: 'name', sortable: true, align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 迁移
const migrationColumns = [
	// { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '虚拟机迁移日志', prop: 'name', sortable: true, align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 监控
const monitorColumns = [
	// { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '性能监控', prop: 'name', sortable: true, align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 告警
const alarmColumns = [
	// { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '告警', prop: 'name', sortable: true, align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
// 任务
const taskColumns = [
	// { label: '单选', width: 60, tdSlot: 'radio', align: 'center', wrap: true },
	{ label: '任务', prop: 'name', sortable: true, align: 'center' },
	{ label: '备注', prop: 'remark', align: 'center' },
];
const vmStatus = (type: string, status: string) => {
	let color = '#ccc';
	let text = '-';
	switch (status) {
		case 'running':
			color = '#1bc91b';
			text = '启动';
			break;
		case 'shutdown':
			text = '正在关机';
			break;
		case 'shutoff':
			color = 'red';
			text = '关机';
			break;
		case 'paused':
			text = '暂停';
			break;
		case 'recover':
			color = '#1bc91b';
			text = '恢复';
			break;
		case 'restart':
			color = '#1bc91b';
			text = '重启';
			break;
			
		default:
			color = '#ccc';
			text = '-';
	}
	if (type == 'color') {
		return color;
	} else {
		return text;
	}
};
const propName = (rule: any, value: any, callback: any) => {
	const regex = /^[\u4e00-\u9fa5_a-zA-Z0-9@_.-]{2,32}$/;
	if (!regex.test(value)) {
		callback(new Error('2-32 个中文、英文、数字、特殊字符@_.-'));
	} else {
		callback();
	}
};
const propCPU = (rule: any, value: any, callback: any) => {
	if (value !== '' && !isNaN(Number(value)) && Number(value) > 0) {
		callback();
	} else {
		callback(new Error('请输入大于等于0的数字'));
	}
};
const propMem = (rule: any, value: any, callback: any) => {
	if (value !== '' && !isNaN(Number(value)) && Number(value) > 0 && Number(value) % 128 === 0) {
		callback();
	} else {
		callback(new Error('请输入请输入128倍数的数字'));
	}
};
const propPassword = (rule: any, value: any, callback: any) => {
	const regex = /^[a-zA-Z0-9@_.]{8,32}$/;
	if (!regex.test(value)) {
		callback(new Error('8-32 个英文、数字、特殊字符(@_.)'));
	} else {
		callback();
	}
};
const propNumber = (rule: any, value: any, callback: any) => {
	const regex = /^[1-9]\d*$/;
	if (!regex.test(value)) {
		callback(new Error('请输入1以上的整数'));
	} else {
		callback();
	}
};
const propIP = (rule: any, value: any, callback: any) => {
	const regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
	if (!regex.test(value)) {
		callback(new Error('请输入有效的IP地址'));
	} else {
		callback();
	}
};
// 字节+单位转换
const capacityConversion = (size: any) => {
	if (size == undefined) {
		return '-';
	}
	const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	let unitIndex = 0;
	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}
	return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
};
// 频率+单位转换
const hzConversion = (size: any) => {
	if (size == undefined) {
		return '-';
	}
	if (size >= 1024) {
		return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GHZ';
	} else {
		return (size / 1).toFixed(1) + ' HZ';
	}
};
// 时间转换
const timeFormat = (time: any) => {
	if (time == undefined) {
		return '-';
	}
	const date = new Date(time);
	const year = date.getFullYear();
	const month = ('0' + (date.getMonth() + 1)).slice(-2);
	const day = ('0' + date.getDate()).slice(-2);
	return `${year}-${month}-${day}`;
};
export {
	tabPoolColumns,
	tabColonyColumns,
	tabHostColumns,
	vmColumns,
	storageColumns,
	vmRecycleColumns,
	netColumns,
	backupsColumns,
	snapshotColumns,
	runColumns,
	migrationColumns,
	monitorColumns,
	alarmColumns,
	taskColumns,
	vmStatus,
	propName,
	propCPU,
	propMem,
	propPassword,
	propNumber,
	propIP,
	capacityConversion,
	hzConversion,
	timeFormat,
};
