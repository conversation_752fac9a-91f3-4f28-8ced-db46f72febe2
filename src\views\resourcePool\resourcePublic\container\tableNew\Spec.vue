<template>
	<el-form ref="formRef" label-position="left" :model="formItem" :rules="rulesForm" label-width="150">
		<el-form-item label="主机名称">
			<el-input v-model="formItem.hostname" placeholder="请输入主机名称" />
		</el-form-item>
    <el-form-item label="运行时">
			<el-input v-model="formItem.runtime" placeholder="请输入创建容器的运行时" />
		</el-form-item>
    <el-form-item label="CPU" prop="cpu">
      <el-input v-model="formItem.cpu" type="number" placeholder="请输入容器虚拟CPU数量" />
		</el-form-item>
    <el-form-item label="内存" prop="memory">
      <el-input v-model="formItem.memory" type="number" placeholder="请输入内存大小(MB)" />
		</el-form-item>
    <el-form-item label="磁盘">
      <el-input v-model="formItem.disk" type="number" placeholder="请输入磁盘大小(MB)" />
		</el-form-item>
    <!-- <el-form-item label="可用域">
      <el-select v-model="formItem.domain" style="width: 100%">
        <el-option label="Select availability zone" value="Select availability zone" />
        <el-option label="nova" value="nova" />
      </el-select>
		</el-form-item> -->
		<el-form-item label="选择策略">
      <el-select v-model="formItem.restartPolicy" style="width: 100%">
        <el-option label="容器退出后不自动重启" value="no" />
        <el-option label="失败时重启" value="on-failure" />
        <el-option label="始终重启" value="Always restart" />
        <el-option label="停止时重启" value="unless-stopped" />
        <el-option label="删除容器" value="Remove-container" />
      </el-select>
		</el-form-item>
    <el-form-item label="最大重试次数">
			<el-input v-model="formItem.retry" type="number" :disabled="formItem.restartPolicy!=='on-failure'" placeholder="请输入最大重试次数" />
		</el-form-item>
    <el-form-item label="启用自动修复">
			<el-checkbox v-model="formItem.heal" />
		</el-form-item>
	</el-form>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { propCPU, propMem } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
  specTime: {
    type: String,
    required: true
  }
});
const formRef = ref<FormInstance>()
const emit = defineEmits(['specOK']);
// 基本信息
const formItem = reactive({
  hostname: '',
  runtime: '',
  cpu: '',
  memory: '',
  disk: '',
  domain: 'Select availability zone',
  restartPolicy: '',
  retry: '',
  heal: false,
});
const rulesForm = reactive<FormRules>({
  cpu: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propCPU, trigger: "change" },
  ],
  memory: [
    { required: true, message: '必填项', trigger: 'blur' },
    { validator: propMem, trigger: "change" },
  ],
})
watch(
  ()=> props.specTime,
  (val)=>{
    console.log('val',val)
    if (formRef.value) {
      formRef.value.validate(val=>{
        if (val) {
          emit('specOK', {
            hostname: formItem.hostname,
            runtime: formItem.runtime,
            cpu: formItem.cpu,
            memory: formItem.memory,
            disk: formItem.disk,
            // domain: formItem.domain, // 占时不用
            restart_policy: {Name:formItem.restartPolicy,MaximumRetryCount:formItem.retry},
            auto_heal: formItem.heal,
          });
        }
      })
    }
  }
);
</script>