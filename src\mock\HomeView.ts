import { getData } from './index';

export const getDefaultInfo = () => {
	const data = {
		software: 'THE VTL',
		version: 'V2.0',
		serial_number: 'GEN00000000000923801037',
		cpu_info: [
			{
				cpu_module: 'HUAWEI,Kunpeng 920',
				cpu_cores: 64,
			},
		],
		fan_status: '正常',
		service_status: '',
		run_time: '1小时12分钟29秒',
	};
	return getData(data);
};

export const getResourceInfo = () => {
	const data = {
		mem_info: {
			mem_total: '132225920',
			mem_free: '123976000',
			mem_available: '123550272',
		},
		cpu_usage_rate: 0.09380863039405794,
		system_status: true,
	};
	return getData(data);
};

export const getDiskInfo = () => {
	const data = [
		{
			name: '/dev/sda',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZR5E986F',
			hctl: '5:0:2:0',
			state: 'running',
		},
		{
			name: '/dev/sdb',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZR617J6B',
			hctl: '5:0:3:0',
			state: 'running',
		},
		{
			name: '/dev/sdc',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0RXA7',
			hctl: '5:0:4:0',
			state: 'running',
		},
		{
			name: '/dev/sdd',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0RCJZ',
			hctl: '5:0:5:0',
			state: '',
		},
		{
			name: '/dev/sde',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0RFP9',
			hctl: '5:0:6:0',
			state: 'running',
		},
		{
			name: '/dev/sdf',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0R79E',
			hctl: '5:0:7:0',
			state: 'running',
		},
		{
			name: '/dev/sdg',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0RQJ0',
			hctl: '5:0:8:0',
			state: 'running',
		},
		{
			name: '/dev/sdh',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0S071',
			hctl: '5:0:9:0',
			state: 'running',
		},
		{
			name: '/dev/sdi',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZR5EANEA',
			hctl: '5:0:10:0',
			state: 'running',
		},
		{
			name: '/dev/sdj',
			rota: true,
			size: '14.6T',
			vendor: 'ATA     ',
			model: 'ST16000NM000J-2TW103',
			rev: 'SN04',
			serial: 'ZRS0RDJV',
			hctl: '5:0:11:0',
			state: 'running',
		},
		{
			name: '/dev/sdk',
			rota: false,
			size: '446.6G',
			vendor: 'AVAGO   ',
			model: 'MR9440-8i',
			rev: '5.14',
			serial: '002c9363272369962d20b1a212b00506',
			hctl: '5:2:0:0',
			state: 'running',
		},
	];
	return getData(data);
};
