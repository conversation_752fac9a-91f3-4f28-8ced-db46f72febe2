<template>
  <div class="resource-pool-container">
    <div class="left-information-area">
      <div class="colony-information-area">
        <p class="information-title"  @click="summaryQuery"><span>虚拟机信息</span></p>
        <!-- <p class="information-title"><span>集群信息</span></p> -->
        <div class="colony-content">
          <img src="../../../assets/resource/xnj.jpg" alt="">
          <div><span>虚拟机名称:</span><span></span><span>{{ state.vmName }}</span></div>
          <div><span>虚拟机IP地址:</span><span></span><span>{{ state.vmIP }}</span></div>
          <div><span>所属物理机:</span><span></span><span>{{ state.hostName }}</span></div>
          <div><span>物理机IP地址:</span><span></span><span>{{ state.hostIP }}</span></div>
          <div><span>系统类型:</span><span></span><span>{{ state.sysType }}</span></div>
          <div><span>系统版本:</span><span></span><span>{{ state.sysVersion }}</span></div>
          <div><span>状态:</span><span></span><span>{{ state.status }}</span></div>
          <div><span>UUID:</span><span></span><span>{{ state.uuid }}</span></div>
          <div><span>VNC端口号:</span><span></span><span>{{ state.vncPort }}</span></div>
          <div><span>SPICE端口号:</span><span></span><span>{{ state.spicePort }}</span></div>
        </div>
      </div>
    </div>
    <div class="middle-information-area">
      <div class="config-information-area">
        <p class="information-title"><span>CPU信息</span></p>
        <div class="config-content">
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cpu.jpg" alt="">
              <div>
                <p><span>CPU总频率:</span><span>{{ state.cpuFrequencyTotal }}</span></p>
                <p><span>已使用频率:</span><span>{{ state.cpuFrequencyUsed }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>CPU使用率</span>
              <el-progress :percentage="state.cpuRate"/>
            </div>
          </div>
        </div>
      </div>
      <div class="config-information-area">
        <p class="information-title"><span>内存信息</span></p>
        <div class="config-content">
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/nc.jpg" alt="">
              <div>
                <p><span>内存:</span><span>{{ state.mem }}</span></p>
                <p><span>已使用内存:</span><span>{{ state.memUsed }}</span></p>
                <p><span>已分配内存:</span><span>{{ state.memAssigned }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>内存使用率</span>
              <el-progress :percentage="state.memRate"/>
            </div>
          </div>
        </div>
      </div>
      <div class="config-information-area">
        <p class="information-title"><span>存储信息</span></p>
        <div class="config-content">
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cc.jpg" alt="">
              <div>
                <p><span>存储:</span><span>{{ state.stor }}</span></p>
                <p><span>已使用存储:</span><span>{{ state.storUsed }}</span></p>
                <p><span>已分配存储:</span><span>{{ state.storAssigned }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>存储使用率</span>
              <el-progress :percentage="state.storRate"/>
            </div>
          </div>
        </div>
      </div>
      <div class="config-information-area">
        <p class="information-title"><span>硬盘信息</span></p>
        <div class="config-content">
          <div class="general-content">
            <div class="general-img">
              <img src="../../../assets/resource/cc.jpg" alt="">
              <div>
                <p><span>名称:</span><span>{{ state.stor }}</span></p>
                <p><span>容量:</span><span>{{ state.storUsed }}</span></p>
              </div>
            </div>
            <div class="general-progress">
              <span>存储使用率</span>
              <el-progress :percentage="state.storRate"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-information-area">
      <div class="strategy-information-area">
        <p class="information-title"><span>策略配置</span></p>
        <div class="information-content">
          <p><span>高可用性:</span><span>{{ state.stor }}</span></p>
          <p><span>自动迁移:</span><span>{{ state.storUsed }}</span></p>
        </div>
      </div>
      <div class="strategy-information-area">
        <p class="information-title"><span>安全信息</span></p>
        <div class="information-content">
          <p><span>安全状态:</span><span>{{ state.stor }}</span></p>
          <p><span>是否加密:</span><span>{{ state.storUsed }}</span></p>
        </div>
      </div>
      <div class="resource-usage-area">
        <p class="information-title"><span>虚拟机杀毒</span></p>
      </div>
      <!-- <div class="resource-usage-area">
        <p class="information-title-long"><span>虚拟机杀毒</span></p>
      </div> -->
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue'
import { dayjs } from 'element-plus';
import { vmOverview } from '/@/api/ResourcePool'; // 接口
import { capacityConversion } from '/@/model/resource.ts'; // 表格 正则
const props = defineProps({
  treeItem: {
    type: Object,
    required: true
  },
  acive: {
    type: String,
    required: true
  }
});
const state = reactive({
  vmName: '-',
  vmIP: '-',
  hostName: '-',
  hostIP: '-',
  sysType: '-',
  sysVersion: '- ',
  status: '-',
  uuid: '-',
  vncPort: '-',
  spicePort: '-',

  cpuFrequencyTotal: '-',
  cpuFrequencyUsed: '-',
  cpuRate: 0,
  mem: '-',
  memUsed: '-',
  memAssigned: '-',
  memRate: 0,
  stor: '-',
  storUsed: '-',
  storAssigned: '-',
  storRate: 0,
});
// 概要 数据
const summaryQuery=()=>{
  vmOverview(props.treeItem.id).then(res=>{
    state.vmName = res.data.name
    state.vmIP = '-'
    state.hostName = res.data.host_name
    state.hostIP = res.data.host_ip
    state.sysType = res.data.os_type
    state.sysVersion = res.data.os_version
    state.status = res.data.status
    state.uuid = res.data.uuid
    state.vncPort = res.data.vnc_port==0?'-':res.data.vnc_port
    state.spicePort = res.data.spice_port==0?'-':res.data.spice_port
  })
}
onMounted(() => {
  summaryQuery()
})
</script>
<style lang="scss" scoped>
  .resource-pool-container {
    width: 100%;
	  height: 100%;
    display: flex;
    justify-content: space-between;
    .left-information-area {
      width: 530px;
      height: calc(100%);
      padding: 5px;
      .colony-information-area {
        width: calc(100%);
        height: calc(100%);
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .colony-content {
          img {
            width: 120px;
          }
          width: 100%;
          height: calc(100% - 50px);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          div {
            span {
              display: inline-block;
              width: 200px;
            }
            span:first-child {
              text-align: right;
            }
            span:nth-child(2) {
              width: 20px;
            }
            span:nth-child(3) {
              width: 280px;
            }
          }
        }
      }
    }
    .middle-information-area {
      width: 500px;
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .config-information-area {
        width: 100%;
        height: 160px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .config-content {
          width: 100%;
          padding: 0 30px;
          height: calc(100% - 50px);
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
        }
      }
    }
    .right-information-area {
      width: 330px;
      height: calc(100%);
      padding: 5px;
      overflow: auto;
      .strategy-information-area {
        width: 100%;
        height: 120px;
        margin-bottom: 20px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .information-content {
          height: 70px;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          span {
            display: inline-block;
            width: 120px;
          }
          span:first-child {
            padding-left: 20px;
          }
          span:nth-child(2) {
            width: 180px;
          }
        }
      }
      .resource-usage-area {
        width: 100%;
        height: 405px;
        border: 1px solid var(--el-card-border-color);
        border-radius: var(--el-card-border-radius);
        box-shadow: var(--el-box-shadow-light);
        .information-title-long {
          height: 40px;
          display: flex;
          align-items: flex-end;
          span {
            display: inline-block;
            text-align: center;
            width: 180px;
            line-height: 30px;
            background-image: url('/@/assets/resource/title.jpg');
            background-size: 100% 100%;
          }
        }
      }
    }
    
  }
  .information-title {
    height: 40px;
    display: flex;
    align-items: flex-end;
    span {
      display: inline-block;
      text-align: center;
      width: 100px;
      line-height: 30px;
		  background-image: url('/@/assets/resource/title.jpg');
      background-size: 100% 100%;
    }
  }
  .general-content {
    height: 100px;
    .general-img {
      display: flex;
      img {
        width: 80px;
      }
      div {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        p {
          span:nth-child(2) {
            margin-left: 15px;
          }
        }
      }
    }
    .general-progress {
      display: flex;
      justify-content: space-between;
      .el-progress {
        width: 350px;
      }
    }
  }
</style>