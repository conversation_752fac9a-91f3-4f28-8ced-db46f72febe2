/* Reset.scss */
/* 重置所有元素的盒模型、边距和填充 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 重置文档样式 */
html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-family: Adobe Heiti Std, Microsoft YaHei, PingFang SC;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
}

/* 重置链接样式 */
a {
  color: var(--el-color-primary);
  text-decoration: none;
  background-color: transparent;
}

a:hover {
  text-decoration: underline;
}

/* 重置图片样式 */
img {
  display: block;
}

/* 重置表单元素 */
input,
button,
select,
optgroup,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  appearance: button;
}

/* 去除输入框轮廓 */
textarea:focus, 
input:focus {
  outline: none;
}

/* 重置列表样式 */
ul, ol {
  list-style: none;
}

/* 表格重置 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 重置标题和段落 */
// h1, h2, h3, h4, h5, h6, p {
//   margin-top: 0;
//   margin-bottom: 0.5rem;
//   font-weight: 500;
//   line-height: 1.2;
// }
