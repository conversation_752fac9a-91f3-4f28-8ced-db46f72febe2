# GUI-DBCtl 数据库管理控制台

## 项目简介

GUI-DBCtl 是一个基于 Vue 3 + TypeScript 的现代化数据库管理控制台，提供了完整的数据库集群监控、管理和运维功能。系统采用前后端分离架构，支持多种数据库类型的统一管理。

## 🚀 技术栈

- **前端框架**: Vue 3.x + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **UI组件库**: Element Plus
- **样式预处理**: SCSS
- **图表组件**: ECharts
- **国际化**: Vue I18n
- **HTTP客户端**: Axios
- **开发工具**: ESLint + Prettier

## 📁 项目结构

```
gui/
├── public/                    # 静态资源
│   ├── favicon.png           # 网站图标
│   └── version.json          # 版本信息
├── src/
│   ├── api/                  # API接口
│   ├── assets/               # 静态资源
│   ├── components/           # 公共组件
│   ├── directive/            # 自定义指令
│   ├── i18n/                 # 国际化配置
│   ├── layout/               # 布局组件
│   ├── router/               # 路由配置
│   ├── stores/               # 状态管理
│   ├── theme/                # 主题样式
│   ├── types/                # TypeScript类型定义
│   ├── utils/                # 工具函数
│   └── views/                # 页面组件
│       ├── DatabaseCenter/   # 数据库管理中心
│       ├── MonitorCenter/    # 监控中心
│       ├── SystemManage/     # 系统管理
│       ├── HomeView/         # 首页
│       ├── login/            # 登录页
│       └── error/            # 错误页面
├── package.json              # 依赖配置
├── vite.config.ts            # Vite配置
└── tsconfig.json             # TypeScript配置
```

## 🎯 核心功能

### 🏠 首页概览
- 系统状态总览
- 关键指标展示
- 快速操作入口

### 📊 监控中心
- **告警管理**: 数据库告警监控和处理
- **日志管理**: 系统日志查看和分析
- **资源统计**: 
  - 关系型数据库监控
  - 时序数据库监控
  - 向量数据库监控
  - 图数据库监控
  - 空间数据库监控
  - 分布式数据库监控

### 🗄️ 数据库管理中心
- **集群管理**: 数据库集群配置和管理
- **主机管理**: 服务器主机监控和管理
- **实例管理**: 数据库实例配置和运维
- **数据库管理**:
  - 数据库创建、删除、配置
  - 用户权限管理
  - 日志查看和分析
  - 性能监控
  - 告警配置
  - 性能分析工具
  - 在线SQL执行器
  - 连接管理
  - 表/视图/角色管理

### ⚙️ 系统管理
- **用户管理**: 系统用户的增删改查
- **角色管理**: 用户角色和权限配置
- **Logo设置**: 系统Logo自定义配置

## 🛠️ 安装与运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 或
yarn dev
```

访问: http://localhost:3000

### 生产构建
```bash
# 构建生产版本
npm run build

# 或
yarn build
```

### 代码检查
```bash
# ESLint检查
npm run lint

# Prettier格式化
npm run format
```

## 🔧 配置说明

### 环境变量
项目支持多环境配置，通过不同的 `.env` 文件管理：

- `.env` - 基础配置
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

### 路由配置
路由采用前端控制模式，支持：
- 动态路由加载
- 权限验证
- 路由懒加载
- 面包屑导航

### 主题配置
支持多主题切换：
- 默认主题
- 深色主题
- 自定义主题配置

## 🌐 国际化

支持多语言切换：
- 简体中文 (zh-cn)
- 繁体中文 (zh-tw)  
- 英语 (en)

## 🔐 权限管理

基于角色的权限控制(RBAC)：
- **admin**: 管理员角色，拥有所有权限
- **common**: 普通用户角色，受限权限

路由级权限控制，按钮级权限控制。

## 📱 响应式设计

完全响应式设计，支持：
- 桌面端 (>= 1200px)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 🎨 UI特性

- 现代化Material Design风格
- 平滑的动画过渡效果
- 灵活的布局系统
- 丰富的图表组件
- 可自定义的主题系统

## 🔄 状态管理

使用Pinia进行状态管理：
- 用户信息管理
- 主题配置管理
- 路由状态管理
- 标签页管理

## 📊 数据可视化

集成ECharts图表库，支持：
- 折线图、柱状图、饼图
- 实时数据更新
- 交互式图表操作
- 自定义图表主题

## 🧪 开发规范

### 代码规范
- 使用TypeScript严格模式
- 遵循ESLint + Prettier规范
- 组件命名采用PascalCase
- 文件命名采用kebab-case

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式化
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

## 📞 技术支持

如有问题或建议，请联系开发团队。

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](./LICENSE) 文件。

---

**开发团队** | **版本** v1.0.0 | **最后更新** 2024年 