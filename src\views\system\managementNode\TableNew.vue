<template>
	<el-dialog v-model="formItem.isShow" title="添加节点" append-to-body class="dialog-500">
		<el-form ref="ruleFormRef" :model="formItem" :rules="rules" label-width="auto">
			<el-form-item label="IP地址" prop="ip">
				<el-input v-model="formItem.ip" placeholder="请输入节点IP地址" />
			</el-form-item>
			<el-form-item label="用户名" prop="username">
				<el-input v-model="formItem.username" placeholder="请输入节点用户名" />
			</el-form-item>
			<el-form-item label="密码" prop="password">
				<el-input v-model="formItem.password" show-password placeholder="请输入节点密码" />
			</el-form-item>
			<el-form-item label="描述" prop="description">
				<el-input v-model="formItem.description" type="textarea" :rows="3" placeholder="请输入节点描述信息" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="formItem.isShow = false">取消</el-button>
				<el-button type="primary" @click="confirm">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { managementNodeAdd } from '/@/api/System'; // 接口
import { propName, propIP } from '/@/model/resource.ts'; // 表列、正则

const props = defineProps({
	newTime: {
		type: String,
		required: true,
	},
});

const ruleFormRef = ref<FormInstance>();
const formItem = reactive({
	isShow: false,
	ip: '',
	username: '',
	password: '',
	description: '',
});

const rules = reactive<FormRules>({
	ip: [
		{ required: true, message: '必填项' },
		{ validator: propIP, trigger: 'blur' },
	],
	username: [
		{ required: true, message: '必填项' },
		{ validator: propName, trigger: 'blur' },
	],
	password: [
		{ required: true, message: '必填项' },
	],
	description: [
		{ required: false },
	],
});

const emit = defineEmits(['returnOK']);
const confirm = () => {
	if (ruleFormRef.value) {
		// 确保 ruleFormRef 已初始化
		ruleFormRef.value.validate((val) => {
			if (val) {
				formItem.isShow = false;
				// 这里使用 userNew API，实际项目中应该使用专门的添加节点 API
				managementNodeAdd({
					ip: formItem.ip,
					username: formItem.username,
					password: formItem.password,
					description: formItem.description,
				}).then((res) => {
					if(res.msg == 'ok') {
						ElMessage.success('添加节点操作完成');
						emit('returnOK', 'refresh');
					} else {
						ElMessage.error(res.msg);
					}
				});
			}
		});
	}
};

watch(
	() => props.newTime,
	(val) => {
		formItem.isShow = true;
		formItem.ip = '';
		formItem.username = '';
		formItem.password = '';
		formItem.description = '';
		if (ruleFormRef.value) {
			// 确保 ruleFormRef 已初始化
			ruleFormRef.value.resetFields();
		}
	}
);
</script>
