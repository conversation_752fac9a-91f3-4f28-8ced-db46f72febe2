// 集群-存储告警
const imgColumns = [
	{ type: 'selection', wrap: true },
	{ type: 'expand', expand: 'expandContent' },
	{ label: '镜像名称', prop: 'name' },
	{ label: '系统类型', prop: 'os_type', align: 'center' },
	{ label: '大小', tdSlot: 'size', align: 'center' },
	{ label: '状态', tdSlot: 'status', align: 'center' },
	{ label: '镜像格式', prop: 'disk_format', align: 'center' },
	{ label: '操作', tdSlot: 'operation', align: 'center', width: 120, wrap: true },
];
const formatSize = (size: any) => {
	if (size == undefined) {
		return '-';
	}
	const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
	let unitIndex = 0;
	while (size >= 1024 && unitIndex < units.length - 1) {
		size /= 1024;
		unitIndex++;
	}
	return Math.floor(size * 100) / 100 + ' ' + units[unitIndex];
};
export { imgColumns, formatSize };
