<template>
	<div>
		<el-dialog v-model="state.isShow" append-to-body class="dialog-900">
			<template #header="{ close, titleId, titleClass }">
				<span class="el-dialog__title">{{ state.switchRow.name }} 交换机-端口组</span>
			</template>
			<div class="dialog-area">
				<div class="tabs-btn-area">
					<div>
						<el-button type="primary" plain @click="newClick">添加端口组</el-button>
						<el-button type="primary" plain @click="refresh">刷新</el-button>
						<el-button type="danger" plain @click="deleteClick(state.tableSelect)">删除</el-button>
					</div>
					<div>
						<el-input v-model="state.tableSearch" style="max-width: 300px" placeholder="请输入搜索内容">
							<template #append>
								<el-button :icon="Search" @click="refresh"></el-button>
							</template>
						</el-input>
					</div>
				</div>
				<div class="table-area">
					<my-table ref="tableRef" :pagination="state.pagination" :columns="state.columns" :request="getTableData" @selectionChange="selectChange">
						<!-- 实际使用量 -->
						<template #allocation="{ row }">
							<span>{{ row.allocation }}</span>
						</template>
						<!-- 操作 -->
						<template #operation="{ row }">
							<el-dropdown trigger="click" @command="commandItem($event, row)">
								<el-button type="primary"
									>操作<el-icon class="el-icon--right"><ArrowDownBold /></el-icon
								></el-button>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item command="bj">修改</el-dropdown-item>
										<el-dropdown-item command="sc" style="color: red" divided>删除</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</template>
					</my-table>
				</div>
			</div>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="state.isShow = false">关闭</el-button>
					<!-- <el-button type="primary" @click="confirm">确认</el-button> -->
				</div>
			</template>
		</el-dialog>
		<TableNew ref="newRef" @returnOK="returnOK" />
    <TableEdit ref="editRef" @returnOK="returnOK" />
		<TableDelete :names="formDelet.tableNames" :deleteTime="state.deleteTime" @returnOK="returnOK"></TableDelete>
	</div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted, nextTick, ref, watch } from 'vue';
import { portGroupQuery,portGroupDelete  } from '/@/api/Network'; // 接口
import { portGroupColumns } from '/@/model/network'; // 表列、正则
import { Search, ArrowDownBold } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const MyTable = defineAsyncComponent(() => import('/@/components/table/MyTable.vue'));
const TableNew = defineAsyncComponent(() => import('./TableNew.vue'));
const TableEdit = defineAsyncComponent(() => import('./TableEdit.vue'));
const TableDelete = defineAsyncComponent(() => import('/@/layout/component/TableDelete.vue'));

const state = reactive({
	isShow: false,
	columns: portGroupColumns,
	pagination: {
		show: true,
	}, // 是否显示分页
	tableSearch: '',
	tableSelect: [],
	deleteTime: '',
	switchRow: {id:'',name:''},
});
interface FormDelet {
  tableNames: string[];
  tableIDs: string[]; // 或 `string[]`
}
const formDelet: FormDelet = {
  tableNames: [],
  tableIDs: []
};
const getTableData = (params: EmptyObjectType, page: EmptyObjectType) => {
  state.tableSelect = []
	return new Promise(async (resolve) => {
		portGroupQuery({
			id: state.switchRow.id, // 存储池ID
			page: page.pageNum, // 当前页
			pagecount: page.pageSize, // 每页条
			order_type: page.order, // 排序规则
			order_by: page.sort, // 排序列
			search_str: state.tableSearch, // 搜索条件
		}).then((res: any) => {
			resolve({
				data: res.data, // 数据
				total: res.total * 1, // 总数
			});
		}).catch((err: any) => {
			resolve({
				data: [], // 数据
				total: 0, // 总数
			});
		});
	});
};
const tableRef = ref();
// 刷新
const refresh = () => {
	tableRef.value.handleSearch(); // 收索事件 表1页
	// tableRef.value.refresh(); // 刷新事件 表当前
};
// const emit = defineEmits(['portReturn']);
const confirm = () => {
	state.isShow = false;
	// emit('portReturn', 'cg');
};
// 添加交换机
const newRef = ref();
const newClick = () => {
	newRef.value.openPortDialog(state.switchRow);
};
// 表操作列
const editRef = ref();
const commandItem = (item: string, row: any) => {
	switch (item) {
		case 'bj':
			editRef.value.openPortDialog(row);
			break;
		case 'sc':
      deleteClick([row])
			break;
	}
};
// 表格选中变化
const selectChange = (row: any) => {
	state.tableSelect = row;
};
// 删除端口组
const deleteClick = (arr:any)=>{
  if(arr.length == 0) {
    ElMessage.warning('未选择数据');
  }else {
    let names:any[] = [];
    let ids:any[] = [];
    arr.forEach((item:any)=>{
      names.push(item.name);
      ids.push(item.id);
    })
    formDelet.tableNames = names
    formDelet.tableIDs = ids
    state.deleteTime = '端口组/'+new Date()
  }
}
// 返回数据
const returnOK = (item: any) => {
	if(item == 'delete') {
    portGroupDelete({
      names: formDelet.tableNames,
      ids: formDelet.tableIDs,
    })
    .then((res:any) => {
      if(res.msg == 'ok'){
        refresh()
        ElMessage.success('删除端口组操作完成');
      }else {
        ElMessage.error('删除端口组操作失败');
      }
    })
  }else {
    refresh()
  }
};
const openDialog = (row: any) => {
  state.isShow = true;
  nextTick(() => {
		state.switchRow = row
		if(tableRef.value){
			refresh();
		}
  })
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.dialog-area {
	height: 650px;
	width: 100%;
	.tabs-btn-area {
		height: 50px;
		display: flex;
		justify-content: space-between;
	}
	.table-area {
		position: relative;
		height: calc(100% - 50px);
		width: 100%;
	}
}
</style>