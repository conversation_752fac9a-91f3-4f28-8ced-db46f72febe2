<template>
	<div v-for="val in chils" :key="val.path">
    <el-menu-item :index="val.path" :key="val.path">
				<template v-if="val.meta && (!val.meta.isLink || (val.meta.isLink && val.meta.isIframe))">
					<SvgSymbol :name="val.meta.icon || ''" />
					<span>{{ val.meta.title ? $t(val.meta.title) : '' }}</span>
				</template>
				<template v-else>
					<a class="w100" @click.prevent="onALinkClick(val)">
						<SvgSymbol :name="val.meta?.icon || ''" />
						{{ val.meta?.title ? $t(val.meta.title) : '' }}
					</a>
				</template>
			</el-menu-item>
	</div>
</template>

<script setup lang="ts" name="navMenuSubItem">
import { computed } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import other from '/@/utils/other';

// 定义类型
interface RouteItem {
  path: string;
  name?: string | symbol | undefined | null;
  redirect?: string;
  meta?: {
    title?: string;
    isLink?: string;
    isHide?: boolean;
    isKeepAlive?: boolean;
    isAffix?: boolean;
    isIframe?: boolean;
    roles?: string[];
    icon?: string;
    isDynamic?: boolean;
    isDynamicPath?: string;
    isIframeOpen?: string;
    loading?: boolean;
  };
  children: any[];
  [key: string]: any;
}

type RouteItems = RouteItem[];

// 定义父组件传过来的值
const props = defineProps({
	// 菜单列表
	chil: {
		type: Array<RouteRecordRaw>,
		default: () => [],
	},
});

// 获取父级菜单数据
const chils = computed(() => {
	return props.chil as RouteItems;
});

// 打开外部链接
const onALinkClick = (val: RouteItem) => {
	other.handleOpenLink(val);
};
</script>
