/* 组件页面公共样式 */

// 组件标题样式
.component-title {
  font-size: 28px;
  margin-bottom: 16px;
  color: var(--el-input-text-color, #303133);
}

// 组件描述样式
.component-description {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin-bottom: 30px;
}

.component-section {
  margin-bottom: 40px;
}

// 组件内部分节标题样式
.section-title {
  font-size: 20px;
  color: var(--el-input-text-color, #303133);
  margin-bottom: 16px;
}

// 组件内部分节描述样式
.section-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 20px;
}

// 示例块样式
.example-block {
  border: 1px solid var(--el-border-color, #e4e7ed);
  border-radius: 4px;
  margin-bottom: 24px;
  background-color: #fff;
}

// 示例演示区域样式
.example-demo {
  padding: 24px;
  border-bottom: 1px dashed var(--el-border-color, #e4e7ed);
}

// 示例代码区域样式
.example-code {
  padding: 16px;
}
